# XWorm-Style RAT Framework 🦠

Framework completo tipo XWorm-RAT para testing de antivirus con generador de malware personalizado, bot de Discord, servidor C2 profesional y técnicas de evasión avanzadas.

## ⚠️ ADVERTENCIA IMPORTANTE

**ESTE PROYECTO ES ÚNICAMENTE PARA FINES EDUCATIVOS Y DE TESTING**

- ✅ Usar para probar tu propio antivirus
- ✅ Usar en entornos de laboratorio controlados
- ✅ Usar para educación en ciberseguridad
- ❌ NO usar para actividades maliciosas
- ❌ NO usar en sistemas que no te pertenezcan
- ❌ NO distribuir con intenciones maliciosas

## 🚀 Características

### Técnicas de Evasión Básicas
- **Detección de Sandbox**: Identifica entornos de análisis
- **Anti-Debugging**: Detecta debuggers y herramientas de análisis
- **Ofuscación de Código**: Técnicas de ocultación de código
- **Comportamiento Polimórfico**: Cambio dinámico de patrones

### Técnicas de Evasión Avanzadas
- **Process Hollowing**: Simulación de inyección de procesos
- **DLL Injection**: Simulación de inyección de bibliotecas
- **API Hooking**: Simulación de interceptación de APIs
- **Rootkit Behavior**: Simulación de ocultación de archivos/registros
- **Memory Evasion**: Técnicas de evasión en memoria
- **Timing-Based Evasion**: Evasión basada en temporización
- **Network Evasion**: Técnicas de evasión de red
- **Anti-Analysis**: Detección de herramientas de análisis

### Técnicas APT Nivel Nation-State
- **UEFI/Firmware Persistence**: Persistencia a nivel firmware
- **Hypervisor Rootkit**: Rootkit a nivel hipervisor (Blue Pill)
- **Advanced Polymorphic Engine**: Motor polimórfico avanzado
- **Sophisticated C2**: Comunicación C2 multi-protocolo
- **Multi-Stage Payload**: Entrega de payload en múltiples etapas
- **Advanced Anti-Forensics**: Anti-forense avanzado
- **Self-Destruction**: Mecanismos de autodestrucción

### Simulación de Comportamientos Maliciosos
- **Keylogger**: Simulación de captura de teclas
- **Network Scanner**: Simulación de escaneo de red
- **File Encryption**: Simulación de cifrado de archivos (tipo ransomware)
- **Persistence**: Simulación de mecanismos de persistencia
- **Cryptocurrency Mining**: Simulación de cryptojacking y minería maliciosa

## 📋 Requisitos

- Python 3.7+
- Windows (algunas técnicas son específicas de Windows)
- Permisos de administrador (recomendado para pruebas completas)

## 🔧 Instalación

1. Clona o descarga el proyecto:
```bash
git clone <repository-url>
cd lilium
```

2. Instala las dependencias:
```bash
pip install -r requirements.txt
```

## 🎯 Uso

### Ejecución Básica
```bash
python ultimate_av_tester.py
```

### Opciones Avanzadas
```bash
# Ejecución con salida detallada
python ultimate_av_tester.py --verbose

# Ejecución rápida (solo tests básicos)
python ultimate_av_tester.py --quick

# Personalizar delay entre tests
python ultimate_av_tester.py --delay 2

# Saltar tests avanzados
python ultimate_av_tester.py --no-advanced

# Saltar simulación de comportamientos
python ultimate_av_tester.py --no-behavior

# Habilitar simulación APT nivel nation-state (EXTREMO)
python ultimate_av_tester.py --apt-level

# Saltar tests APT
python ultimate_av_tester.py --no-apt

# Habilitar simulación de cryptomining
python ultimate_av_tester.py --cryptomining

# Saltar tests de cryptomining
python ultimate_av_tester.py --no-mining

# Minería real educativa con tu wallet
python ultimate_av_tester.py --educational-mining --wallet TU_WALLET_XMR

# Configurar wallet para minería
python wallet_setup_guide.py
```

### Ayuda
```bash
python ultimate_av_tester.py --help
```

## 📊 Reportes

El framework genera varios tipos de reportes:

1. **Consola**: Resultados en tiempo real
2. **Log File**: `av_test_results.log` - Log detallado
3. **JSON Report**: `ultimate_av_test_report_YYYYMMDD_HHMMSS.json` - Reporte completo

### Ejemplo de Reporte JSON
```json
{
  "test_session": {
    "start_time": "2024-01-15T10:30:00",
    "end_time": "2024-01-15T10:35:30",
    "duration_seconds": 330
  },
  "summary": {
    "total_techniques_tested": 15,
    "detected_by_av": 12,
    "bypassed_av": 3,
    "detection_rate_percentage": 80.0,
    "bypass_rate_percentage": 20.0
  },
  "detailed_results": [...],
  "recommendations": [...]
}
```

## 🧪 Técnicas Implementadas

### Evasión Básica
| Técnica | Descripción | Nivel |
|---------|-------------|-------|
| Sandbox Detection | Detecta VMs y entornos de análisis | Básico |
| Anti-Debugging | Detecta debuggers activos | Básico |
| Code Obfuscation | Ofuscación de strings y código | Básico |
| Polymorphic Behavior | Cambio dinámico de patrones | Básico |

### Evasión Avanzada
| Técnica | Descripción | Nivel |
|---------|-------------|-------|
| Process Hollowing | Inyección en procesos legítimos | Avanzado |
| DLL Injection | Inyección de bibliotecas | Avanzado |
| API Hooking | Interceptación de llamadas API | Avanzado |
| Rootkit Behavior | Ocultación de archivos/registros | Avanzado |
| Memory Evasion | Técnicas de evasión en memoria | Avanzado |
| Network Evasion | DGA, tráfico cifrado | Avanzado |

### Técnicas APT Nation-State
| Técnica | Descripción | Nivel |
|---------|-------------|-------|
| UEFI Persistence | Persistencia a nivel firmware/UEFI | Nation-State |
| Hypervisor Rootkit | Rootkit a nivel hipervisor (Blue Pill) | Nation-State |
| Polymorphic Engine | Motor polimórfico avanzado multi-capa | Nation-State |
| Sophisticated C2 | DNS tunneling, domain fronting, blockchain | Nation-State |
| Multi-Stage Payload | Entrega escalonada con validación | Nation-State |
| Anti-Forensics | Manipulación de timeline, destrucción evidencia | Nation-State |
| Self-Destruction | Autodestrucción con dead man's switch | Nation-State |

### Comportamientos Maliciosos
| Comportamiento | Descripción | Riesgo |
|----------------|-------------|--------|
| Keylogger | Captura de teclas (simulado) | Alto |
| Network Scanner | Escaneo de puertos | Medio |
| File Encryption | Cifrado de archivos | Alto |
| Persistence | Mecanismos de persistencia | Medio |
| Cryptocurrency Mining | Minería maliciosa de criptomonedas | Alto |

### Técnicas de Cryptomining
| Técnica | Descripción | Tipo |
|---------|-------------|------|
| Stealth CPU Mining | Minería sigilosa con throttling de CPU | Cryptojacking |
| GPU Mining Detection | Detección y uso de GPU para minería | Cryptojacking |
| Browser-Based Mining | Cryptojacking vía JavaScript en navegador | Web-based |
| Process Hiding | Ocultación de procesos de minería | Stealth |
| Thermal Management | Gestión térmica para evitar detección | Evasion |
| Pool Communication | Comunicación cifrada con mining pools | Network |

## 📈 Interpretación de Resultados

### Tasas de Detección
- **90-100%**: Excelente protección
- **80-89%**: Buena protección
- **70-79%**: Protección moderada
- **<70%**: Protección insuficiente

### Recomendaciones por Tasa de Detección

#### Detección Baja (<50%)
- ⚠️ Actualizar definiciones de virus
- ⚠️ Habilitar protección en tiempo real
- ⚠️ Activar análisis heurístico
- ⚠️ Considerar cambiar de antivirus

#### Detección Moderada (50-80%)
- ⚡ Habilitar capas adicionales de protección
- ⚡ Verificar configuración de comportamiento
- ⚡ Asegurar actualizaciones automáticas

#### Detección Alta (>80%)
- ✅ Mantener configuración actual
- ✅ Continuar con actualizaciones regulares
- ✅ Considerar protección adicional (firewall, IDS)

## 🔍 Análisis de Resultados

### Técnicas Comúnmente Detectadas
- Keylogger simulation
- File encryption
- Registry persistence
- Network scanning

### Técnicas Que Pueden Evadir
- Sandbox detection
- Anti-debugging
- Code obfuscation
- Timing-based evasion

## 🛠️ Desarrollo y Contribución

### Estructura del Proyecto
```
lilium/
├── ultimate_av_tester.py    # Script principal
├── malware_tester.py        # Técnicas básicas
├── advanced_evasion.py      # Técnicas avanzadas
├── requirements.txt         # Dependencias
└── README.md               # Documentación
```

### Añadir Nuevas Técnicas

1. Para técnicas básicas: editar `malware_tester.py`
2. Para técnicas avanzadas: editar `advanced_evasion.py`
3. Seguir el patrón de logging existente
4. Documentar la nueva técnica

## 📚 Recursos Educativos

### Libros Recomendados
- "Malware Analyst's Cookbook" - Michael Ligh
- "Practical Malware Analysis" - Michael Sikorski
- "The Art of Memory Forensics" - Michael Hale Ligh

### Cursos Online
- SANS FOR610: Reverse-Engineering Malware
- Cybrary: Malware Analysis
- Pluralsight: Ethical Hacking

### Herramientas Complementarias
- **Análisis Estático**: IDA Pro, Ghidra, Radare2
- **Análisis Dinámico**: Process Monitor, Wireshark, Volatility
- **Sandboxes**: Cuckoo Sandbox, Any.run, Hybrid Analysis

## ⚖️ Consideraciones Legales

- Usar solo en sistemas propios o con autorización explícita
- No distribuir con intenciones maliciosas
- Cumplir con las leyes locales de ciberseguridad
- Usar únicamente para educación y testing legítimo

## 🤝 Soporte

Para preguntas educativas o problemas técnicos:
1. Revisar la documentación
2. Verificar los logs de error
3. Comprobar permisos y dependencias

## 📄 Licencia

Este proyecto es únicamente para fines educativos. No se permite el uso malicioso.

---

**Recuerda**: La ciberseguridad es un campo que requiere ética y responsabilidad. Usa estas herramientas para aprender y mejorar las defensas, nunca para causar daño.
