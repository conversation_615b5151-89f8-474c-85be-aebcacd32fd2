#!/usr/bin/env python3
"""
Educational Malware Tester for Antivirus Evaluation
==================================================
This is an educational tool designed to test antivirus effectiveness.
DO NOT USE FOR MALICIOUS PURPOSES - FOR EDUCATIONAL/TESTING ONLY

Author: Educational Security Research
Purpose: Antivirus testing and cybersecurity education
"""

import os
import sys
import time
import random
import string
import socket
import threading
import subprocess
import winreg
import ctypes
from ctypes import wintypes
import hashlib
import base64
import json
import requests
from datetime import datetime

class AntivirusEvasion:
    """Implements various antivirus evasion techniques for testing"""
    
    def __init__(self):
        self.log_file = "av_test_results.log"
        self.techniques_tested = []
        
    def log_result(self, technique, detected=False):
        """Log whether a technique was detected by AV"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        status = "DETECTED" if detected else "BYPASSED"
        log_entry = f"[{timestamp}] {technique}: {status}\n"
        
        with open(self.log_file, "a") as f:
            f.write(log_entry)
        
        self.techniques_tested.append({
            "technique": technique,
            "detected": detected,
            "timestamp": timestamp
        })
        print(f"[TEST] {technique}: {status}")

    def sandbox_detection(self):
        """Detect if running in a sandbox environment"""
        try:
            # Check for common sandbox indicators
            sandbox_indicators = [
                # VM detection
                lambda: os.path.exists("C:\\windows\\system32\\drivers\\vmmouse.sys"),
                lambda: os.path.exists("C:\\windows\\system32\\drivers\\vmhgfs.sys"),
                lambda: "vmware" in subprocess.check_output("wmic computersystem get model", shell=True).decode().lower(),
                
                # Analysis tools
                lambda: os.path.exists("C:\\analysis"),
                lambda: os.path.exists("C:\\sandbox"),
                lambda: "wireshark" in str(subprocess.check_output("tasklist", shell=True)).lower(),
                
                # Low resource indicators
                lambda: os.cpu_count() < 2,
                lambda: ctypes.windll.kernel32.GetTickCount() < 300000,  # Less than 5 minutes uptime
            ]
            
            detected_indicators = sum(1 for check in sandbox_indicators if check())
            is_sandbox = detected_indicators >= 2
            
            self.log_result("Sandbox Detection", not is_sandbox)
            return is_sandbox
            
        except Exception as e:
            self.log_result("Sandbox Detection", True)
            return False

    def anti_debugging(self):
        """Implement anti-debugging techniques"""
        try:
            # Check for debugger presence
            kernel32 = ctypes.windll.kernel32
            
            # IsDebuggerPresent check
            if kernel32.IsDebuggerPresent():
                self.log_result("Anti-Debugging (IsDebuggerPresent)", True)
                return True
            
            # CheckRemoteDebuggerPresent check
            debug_flag = ctypes.c_bool()
            kernel32.CheckRemoteDebuggerPresent(kernel32.GetCurrentProcess(), ctypes.byref(debug_flag))
            if debug_flag.value:
                self.log_result("Anti-Debugging (CheckRemoteDebuggerPresent)", True)
                return True
            
            self.log_result("Anti-Debugging", False)
            return False
            
        except Exception as e:
            self.log_result("Anti-Debugging", True)
            return True

    def code_obfuscation(self):
        """Demonstrate code obfuscation techniques"""
        try:
            # String obfuscation using base64
            obfuscated_string = base64.b64encode(b"This is a test payload").decode()
            decoded = base64.b64decode(obfuscated_string).decode()
            
            # Dynamic function name generation
            func_name = ''.join(random.choices(string.ascii_letters, k=10))
            
            # XOR encryption/decryption
            key = 0x42
            encrypted_data = bytes([b ^ key for b in b"sensitive_data"])
            decrypted_data = bytes([b ^ key for b in encrypted_data])
            
            self.log_result("Code Obfuscation", False)
            return True
            
        except Exception as e:
            self.log_result("Code Obfuscation", True)
            return False

    def polymorphic_behavior(self):
        """Implement basic polymorphic behavior"""
        try:
            # Generate random file names and paths
            random_filename = ''.join(random.choices(string.ascii_letters + string.digits, k=12)) + ".tmp"
            temp_path = os.path.join(os.environ.get('TEMP', '/tmp'), random_filename)
            
            # Create temporary file with random content
            random_content = os.urandom(random.randint(100, 1000))
            with open(temp_path, 'wb') as f:
                f.write(random_content)
            
            # Clean up
            time.sleep(0.1)
            os.remove(temp_path)
            
            self.log_result("Polymorphic Behavior", False)
            return True
            
        except Exception as e:
            self.log_result("Polymorphic Behavior", True)
            return False

class MaliciousBehaviorSimulator:
    """Simulates various malicious behaviors for testing"""
    
    def __init__(self, evasion_tester):
        self.evasion = evasion_tester
        
    def simulate_keylogger(self):
        """Simulate keylogger behavior (without actually logging keys)"""
        try:
            # Simulate keyboard hook installation
            user32 = ctypes.windll.user32
            kernel32 = ctypes.windll.kernel32
            
            # Just check if we can access the required functions
            hook_proc = ctypes.WINFUNCTYPE(ctypes.c_int, ctypes.c_int, wintypes.WPARAM, wintypes.LPARAM)
            
            def low_level_keyboard_proc(nCode, wParam, lParam):
                return user32.CallNextHookEx(None, nCode, wParam, lParam)
            
            # Simulate without actually installing hook
            self.evasion.log_result("Keylogger Simulation", False)
            return True
            
        except Exception as e:
            self.evasion.log_result("Keylogger Simulation", True)
            return False

    def simulate_network_scanner(self):
        """Simulate network scanning behavior"""
        try:
            # Simulate port scanning on localhost
            common_ports = [21, 22, 23, 25, 53, 80, 110, 443, 993, 995]
            
            for port in common_ports[:3]:  # Test only first 3 ports
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(0.1)
                    result = sock.connect_ex(('127.0.0.1', port))
                    sock.close()
                except:
                    pass
            
            self.evasion.log_result("Network Scanner Simulation", False)
            return True
            
        except Exception as e:
            self.evasion.log_result("Network Scanner Simulation", True)
            return False

    def simulate_file_encryption(self):
        """Simulate ransomware-like file encryption"""
        try:
            # Create test file
            test_file = "test_encryption_target.txt"
            test_content = "This is a test file for encryption simulation"
            
            with open(test_file, 'w') as f:
                f.write(test_content)
            
            # Simulate encryption (simple XOR)
            key = b'test_key_123'
            with open(test_file, 'rb') as f:
                data = f.read()
            
            encrypted_data = bytes([data[i] ^ key[i % len(key)] for i in range(len(data))])
            
            with open(test_file + ".encrypted", 'wb') as f:
                f.write(encrypted_data)
            
            # Clean up
            os.remove(test_file)
            os.remove(test_file + ".encrypted")
            
            self.evasion.log_result("File Encryption Simulation", False)
            return True
            
        except Exception as e:
            self.evasion.log_result("File Encryption Simulation", True)
            return False

    def simulate_persistence(self):
        """Simulate persistence mechanisms"""
        try:
            # Simulate registry persistence (read-only test)
            try:
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                                   r"Software\Microsoft\Windows\CurrentVersion\Run", 
                                   0, winreg.KEY_READ)
                winreg.CloseKey(key)
            except:
                pass
            
            # Simulate startup folder access
            startup_folder = os.path.join(os.environ.get('APPDATA', ''), 
                                        'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup')
            if os.path.exists(startup_folder):
                os.listdir(startup_folder)
            
            self.evasion.log_result("Persistence Simulation", False)
            return True
            
        except Exception as e:
            self.evasion.log_result("Persistence Simulation", True)
            return False

def main():
    """Main function to run all tests"""
    print("=" * 60)
    print("Educational Malware Tester for Antivirus Evaluation")
    print("=" * 60)
    print("WARNING: This is for educational/testing purposes only!")
    print("Do not use for malicious activities.")
    print("=" * 60)
    
    # Initialize components
    evasion_tester = AntivirusEvasion()
    behavior_simulator = MaliciousBehaviorSimulator(evasion_tester)
    
    # Run evasion tests
    print("\n[PHASE 1] Testing Evasion Techniques...")
    evasion_tester.sandbox_detection()
    time.sleep(1)
    
    evasion_tester.anti_debugging()
    time.sleep(1)
    
    evasion_tester.code_obfuscation()
    time.sleep(1)
    
    evasion_tester.polymorphic_behavior()
    time.sleep(1)
    
    # Run behavior simulations
    print("\n[PHASE 2] Testing Malicious Behavior Detection...")
    behavior_simulator.simulate_keylogger()
    time.sleep(1)
    
    behavior_simulator.simulate_network_scanner()
    time.sleep(1)
    
    behavior_simulator.simulate_file_encryption()
    time.sleep(1)
    
    behavior_simulator.simulate_persistence()
    time.sleep(1)
    
    # Generate report
    print("\n[PHASE 3] Generating Test Report...")
    detected_count = sum(1 for test in evasion_tester.techniques_tested if test['detected'])
    total_count = len(evasion_tester.techniques_tested)
    
    print(f"\nTest Results Summary:")
    print(f"Total techniques tested: {total_count}")
    print(f"Detected by AV: {detected_count}")
    print(f"Bypassed AV: {total_count - detected_count}")
    print(f"Detection rate: {(detected_count/total_count)*100:.1f}%")
    
    print(f"\nDetailed results saved to: {evasion_tester.log_file}")
    
    # Save JSON report
    with open("av_test_report.json", "w") as f:
        json.dump({
            "summary": {
                "total_tests": total_count,
                "detected": detected_count,
                "bypassed": total_count - detected_count,
                "detection_rate": (detected_count/total_count)*100
            },
            "detailed_results": evasion_tester.techniques_tested
        }, f, indent=2)
    
    print("JSON report saved to: av_test_report.json")

if __name__ == "__main__":
    main()
