#!/usr/bin/env python3
"""
XMR Wallet Setup Guide
=====================
Complete guide for setting up Monero (XMR) wallets
for educational mining purposes.

This guide covers:
- Creating new XMR wallets
- Configuring existing wallets
- Wallet security best practices
- Mining pool setup
- Payout configuration

EDUCATIONAL PURPOSE: Learn wallet management and mining setup
"""

import os
import sys
import json
import time
from datetime import datetime

def print_wallet_guide_banner():
    """Print wallet setup guide banner"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                   XMR WALLET SETUP GUIDE                    ║
║              Complete Monero Wallet Configuration           ║
╠══════════════════════════════════════════════════════════════╣
║  💰 MONERO WALLET CONFIGURATION FOR MINING 💰               ║
║                                                              ║
║  This guide will help you:                                   ║
║  • Create or configure XMR wallets                         ║
║  • Set up mining to your personal wallet                   ║
║  • Understand wallet security                              ║
║  • Configure mining pools and payouts                      ║
║  • Monitor your mining earnings                            ║
║                                                              ║
║  Wallet Options:                                             ║
║  • Official Monero GUI Wallet                              ║
║  • Monero CLI Wallet                                       ║
║  • Web Wallets (MyMonero, etc.)                           ║
║  • Hardware Wallets (Ledger, Trezor)                      ║
║  • Mobile Wallets (Monerujo, Cake Wallet)                 ║
║                                                              ║
║  🔒 Security is paramount when handling real cryptocurrency  ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def wallet_creation_guide():
    """Guide for creating new XMR wallets"""
    print("\n🆕 CREATING A NEW MONERO WALLET")
    print("=" * 50)
    
    wallet_options = [
        {
            "name": "Official Monero GUI Wallet",
            "description": "User-friendly graphical interface",
            "download": "https://getmonero.org/downloads/",
            "pros": ["Official", "Full node capability", "Most secure"],
            "cons": ["Large download", "Sync time required"]
        },
        {
            "name": "Monero CLI Wallet", 
            "description": "Command-line interface for advanced users",
            "download": "https://getmonero.org/downloads/",
            "pros": ["Lightweight", "Full control", "Scriptable"],
            "cons": ["Technical knowledge required", "Command-line only"]
        },
        {
            "name": "MyMonero Web Wallet",
            "description": "Browser-based wallet",
            "download": "https://mymonero.com/",
            "pros": ["Quick setup", "No download required", "Easy to use"],
            "cons": ["Less secure", "Requires internet", "Third-party service"]
        },
        {
            "name": "Hardware Wallets",
            "description": "Physical devices for maximum security",
            "download": "Ledger Nano S/X, Trezor Model T",
            "pros": ["Maximum security", "Offline storage", "Multi-currency"],
            "cons": ["Cost money", "Physical device needed", "Setup complexity"]
        }
    ]
    
    print("📱 WALLET OPTIONS:")
    for i, wallet in enumerate(wallet_options, 1):
        print(f"\n{i}. {wallet['name']}")
        print(f"   📝 {wallet['description']}")
        print(f"   🔗 {wallet['download']}")
        print(f"   ✅ Pros: {', '.join(wallet['pros'])}")
        print(f"   ⚠️  Cons: {', '.join(wallet['cons'])}")
    
    print(f"\n💡 RECOMMENDATION FOR BEGINNERS:")
    print(f"   Start with MyMonero for quick setup, then move to")
    print(f"   Official GUI Wallet for better security.")

def wallet_security_guide():
    """Guide for wallet security best practices"""
    print("\n🔒 WALLET SECURITY BEST PRACTICES")
    print("=" * 50)
    
    security_practices = [
        {
            "category": "🔑 Seed Phrase Security",
            "practices": [
                "Write down your 25-word seed phrase on paper",
                "Store multiple copies in secure locations",
                "NEVER store seed phrase digitally or online",
                "Test seed phrase recovery before using wallet",
                "Consider metal backup for fire/water protection"
            ]
        },
        {
            "category": "💻 Computer Security",
            "practices": [
                "Use updated antivirus software",
                "Keep operating system updated",
                "Use dedicated computer for crypto if possible",
                "Avoid public WiFi for wallet access",
                "Use VPN for additional privacy"
            ]
        },
        {
            "category": "🌐 Online Security",
            "practices": [
                "Always verify wallet download sources",
                "Check file hashes before installation",
                "Use official websites only",
                "Enable 2FA where available",
                "Be wary of phishing attempts"
            ]
        },
        {
            "category": "💰 Transaction Security",
            "practices": [
                "Always verify recipient addresses",
                "Start with small test transactions",
                "Double-check transaction details",
                "Use subaddresses for privacy",
                "Keep transaction records"
            ]
        }
    ]
    
    for category in security_practices:
        print(f"\n{category['category']}")
        for practice in category['practices']:
            print(f"   • {practice}")
    
    print(f"\n⚠️  CRITICAL REMINDERS:")
    print(f"   • Your seed phrase = your money. Protect it!")
    print(f"   • Monero transactions are irreversible")
    print(f"   • Always verify before sending")
    print(f"   • When in doubt, ask for help")

def mining_pool_configuration():
    """Guide for configuring mining pools"""
    print("\n🏊 MINING POOL CONFIGURATION")
    print("=" * 50)
    
    popular_pools = [
        {
            "name": "SupportXMR",
            "url": "pool.supportxmr.com",
            "ports": {"Low-end CPUs": 3333, "High-end CPUs": 5555, "SSL": 443},
            "fee": "0.6%",
            "min_payout": "0.1 XMR",
            "features": ["Large pool", "Reliable", "Good documentation"]
        },
        {
            "name": "XMRPool.eu",
            "url": "xmrpool.eu",
            "ports": {"Standard": 3333, "SSL": 9999},
            "fee": "0.6%", 
            "min_payout": "0.3 XMR",
            "features": ["European-based", "Good uptime", "Detailed stats"]
        },
        {
            "name": "MineXMR",
            "url": "pool.minexmr.com",
            "ports": {"Low difficulty": 4444, "Medium": 3333, "High": 7777},
            "fee": "1.0%",
            "min_payout": "0.1 XMR",
            "features": ["Established pool", "Multiple ports", "Good support"]
        }
    ]
    
    print("🏊 POPULAR MONERO MINING POOLS:")
    for pool in popular_pools:
        print(f"\n📊 {pool['name']}")
        print(f"   🔗 URL: {pool['url']}")
        print(f"   🔌 Ports: {pool['ports']}")
        print(f"   💸 Fee: {pool['fee']}")
        print(f"   💰 Min Payout: {pool['min_payout']}")
        print(f"   ⭐ Features: {', '.join(pool['features'])}")
    
    print(f"\n⚙️  POOL SELECTION CRITERIA:")
    criteria = [
        "Pool size (larger = more consistent payouts)",
        "Fee structure (lower fees = more profit)",
        "Minimum payout (lower = faster payouts)",
        "Server location (closer = lower latency)",
        "Pool reliability and uptime",
        "Community reputation and support"
    ]
    
    for criterion in criteria:
        print(f"   • {criterion}")

def configure_mining_to_wallet():
    """Interactive wallet configuration for mining"""
    print("\n⚙️  CONFIGURE MINING TO YOUR WALLET")
    print("=" * 50)
    
    print("📝 STEP-BY-STEP CONFIGURATION:")
    
    # Step 1: Get wallet address
    print(f"\n1️⃣  GET YOUR WALLET ADDRESS")
    print(f"   • Open your Monero wallet")
    print(f"   • Go to 'Receive' or 'Address' section")
    print(f"   • Copy your primary address (starts with '4')")
    print(f"   • XMR addresses are 95 characters long")
    
    wallet_address = input(f"\n   Enter your XMR wallet address: ").strip()
    
    if len(wallet_address) == 95 and wallet_address.startswith('4'):
        print(f"   ✅ Valid XMR address detected")
        print(f"   📋 Address: {wallet_address[:20]}...{wallet_address[-10:]}")
    else:
        print(f"   ⚠️  Invalid address format. Please check and try again.")
        return None
    
    # Step 2: Choose mining pool
    print(f"\n2️⃣  CHOOSE MINING POOL")
    pools = ["pool.supportxmr.com:443", "xmrpool.eu:9999", "pool.minexmr.com:3333"]
    
    for i, pool in enumerate(pools, 1):
        print(f"   {i}. {pool}")
    
    try:
        pool_choice = int(input(f"\n   Select pool (1-3): "))
        if 1 <= pool_choice <= 3:
            selected_pool = pools[pool_choice - 1]
            print(f"   ✅ Selected: {selected_pool}")
        else:
            selected_pool = pools[0]
            print(f"   📝 Using default: {selected_pool}")
    except:
        selected_pool = pools[0]
        print(f"   📝 Using default: {selected_pool}")
    
    # Step 3: Configure mining intensity
    print(f"\n3️⃣  CONFIGURE MINING INTENSITY")
    print(f"   Educational mining uses limited resources for safety")
    
    intensity_options = {
        1: {"name": "Very Light", "cpu": 15, "threads": 1, "duration": 60},
        2: {"name": "Light", "cpu": 25, "threads": 2, "duration": 120},
        3: {"name": "Moderate", "cpu": 40, "threads": 3, "duration": 300}
    }
    
    for key, option in intensity_options.items():
        print(f"   {key}. {option['name']} - {option['cpu']}% CPU, {option['threads']} threads, {option['duration']}s")
    
    try:
        intensity = int(input(f"\n   Select intensity (1-3): "))
        if intensity not in intensity_options:
            intensity = 1
    except:
        intensity = 1
    
    selected_intensity = intensity_options[intensity]
    print(f"   ✅ Selected: {selected_intensity['name']}")
    
    # Step 4: Generate configuration
    config = {
        "wallet_address": wallet_address,
        "pool_url": selected_pool,
        "pool_password": "educational_mining",
        "threads": selected_intensity["threads"],
        "max_cpu_usage": selected_intensity["cpu"],
        "mining_duration": selected_intensity["duration"],
        "timestamp": datetime.now().isoformat()
    }
    
    # Save configuration
    config_file = "my_mining_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n4️⃣  CONFIGURATION COMPLETE")
    print(f"   ✅ Configuration saved to: {config_file}")
    print(f"   💰 Mining to: {wallet_address[:20]}...")
    print(f"   🏊 Pool: {selected_pool}")
    print(f"   ⚡ Intensity: {selected_intensity['name']}")
    
    return config

def start_mining_with_config():
    """Start mining with saved configuration"""
    print("\n🚀 START MINING WITH YOUR CONFIGURATION")
    print("=" * 50)
    
    config_file = "my_mining_config.json"
    
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print(f"📋 LOADED CONFIGURATION:")
        print(f"   💰 Wallet: {config['wallet_address'][:20]}...")
        print(f"   🏊 Pool: {config['pool_url']}")
        print(f"   ⚡ Threads: {config['threads']}")
        print(f"   🔥 CPU Limit: {config['max_cpu_usage']}%")
        print(f"   ⏰ Duration: {config['mining_duration']}s")
        
        start_mining = input(f"\n   Start mining with this configuration? (y/N): ").lower().strip()
        
        if start_mining == 'y':
            try:
                from educational_xmr_miner import EducationalXMRMiner
                
                print(f"\n🎯 STARTING EDUCATIONAL MINING...")
                miner = EducationalXMRMiner(
                    wallet_address=config['wallet_address'],
                    pool_url=config['pool_url'].split(':')[0] + ':' + config['pool_url'].split(':')[1]
                )
                
                # Override configuration
                miner.config.update({
                    "threads": config['threads'],
                    "max_cpu_usage": config['max_cpu_usage'],
                    "mining_duration": config['mining_duration']
                })
                
                # Start mining
                miner.educational_mining_simulation()
                
                print(f"\n✅ Mining completed! Check your wallet for earnings.")
                
            except Exception as e:
                print(f"❌ Mining failed: {e}")
        else:
            print(f"   Mining cancelled.")
    else:
        print(f"❌ No configuration found. Please run wallet configuration first.")

def monitor_earnings():
    """Guide for monitoring mining earnings"""
    print("\n📊 MONITORING YOUR MINING EARNINGS")
    print("=" * 50)
    
    monitoring_methods = [
        {
            "method": "Pool Website",
            "description": "Check your stats on the mining pool website",
            "steps": [
                "Go to your pool's website",
                "Enter your wallet address in the search",
                "View hashrate, shares, and pending balance",
                "Check payout history"
            ]
        },
        {
            "method": "Wallet Balance",
            "description": "Check your wallet for received payments",
            "steps": [
                "Open your Monero wallet",
                "Wait for blockchain sync (if using full node)",
                "Check 'Transactions' or 'History' tab",
                "Look for incoming transactions from pool"
            ]
        },
        {
            "method": "Block Explorers",
            "description": "Use online tools to verify transactions",
            "steps": [
                "Visit xmrchain.net or localmonero.co/blocks",
                "Search for your wallet address",
                "View transaction history and confirmations",
                "Verify transaction details"
            ]
        }
    ]
    
    for method in monitoring_methods:
        print(f"\n📈 {method['method']}")
        print(f"   📝 {method['description']}")
        for step in method['steps']:
            print(f"   • {step}")
    
    print(f"\n💡 EARNINGS EXPECTATIONS:")
    print(f"   • Educational mining earns very small amounts")
    print(f"   • Payouts depend on pool minimum thresholds")
    print(f"   • First payout may take time to reach minimum")
    print(f"   • Check pool stats for estimated earnings")

def main():
    """Main wallet setup guide"""
    print_wallet_guide_banner()
    
    print(f"\n🎯 WALLET SETUP GUIDE MENU")
    print("=" * 50)
    
    options = [
        ("1", "Create New XMR Wallet", wallet_creation_guide),
        ("2", "Wallet Security Guide", wallet_security_guide),
        ("3", "Mining Pool Information", mining_pool_configuration),
        ("4", "Configure Mining to Wallet", configure_mining_to_wallet),
        ("5", "Start Mining with Config", start_mining_with_config),
        ("6", "Monitor Earnings", monitor_earnings),
        ("7", "Complete Setup Process", lambda: complete_setup_process())
    ]
    
    for option_key, option_name, _ in options:
        print(f"   {option_key}. {option_name}")
    
    while True:
        choice = input(f"\nSelect option (1-7, 0 to exit): ").strip()
        
        if choice == '0':
            print("Exiting wallet setup guide.")
            break
        
        for option_key, option_name, option_func in options:
            if choice == option_key:
                print(f"\n{'='*60}")
                print(f"📖 {option_name}")
                print(f"{'='*60}")
                
                if option_key == "7":
                    complete_setup_process()
                else:
                    option_func()
                
                input(f"\nPress Enter to continue...")
                break
        else:
            print("Invalid option. Please try again.")

def complete_setup_process():
    """Complete wallet setup process"""
    print("🎯 COMPLETE WALLET SETUP PROCESS")
    print("This will guide you through the entire process step by step.")
    
    steps = [
        ("Create/Configure Wallet", wallet_creation_guide),
        ("Learn Security Practices", wallet_security_guide),
        ("Choose Mining Pool", mining_pool_configuration),
        ("Configure Mining", configure_mining_to_wallet),
        ("Start Mining", start_mining_with_config)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"\n{'='*60}")
        print(f"STEP {i}: {step_name}")
        print(f"{'='*60}")
        
        step_func()
        
        if i < len(steps):
            continue_setup = input(f"\nContinue to next step? (y/N): ").lower().strip()
            if continue_setup != 'y':
                print("Setup process stopped.")
                break
    
    print(f"\n🎉 WALLET SETUP COMPLETE!")
    print(f"You're now ready to mine XMR to your personal wallet.")

if __name__ == "__main__":
    main()
