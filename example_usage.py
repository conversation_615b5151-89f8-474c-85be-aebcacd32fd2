#!/usr/bin/env python3
"""
Example Usage of Ultimate Antivirus Tester
==========================================
This script demonstrates how to use the Ultimate AV Tester
framework for educational purposes.

WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY
"""

import sys
import time
from datetime import datetime

# Import the main tester
from ultimate_av_tester import UltimateAVTester

def example_basic_test():
    """Example: Basic antivirus test"""
    print("="*60)
    print("EXAMPLE 1: Basic Antivirus Test")
    print("="*60)
    
    # Create tester with basic configuration
    tester = UltimateAVTester(verbose=True, delay=0.5)
    
    # Disable advanced tests for quick testing
    tester.test_phases['advanced_evasion'] = False
    tester.test_phases['modern_evasion'] = False
    
    # Run the test
    tester.run_all_tests()

def example_comprehensive_test():
    """Example: Comprehensive antivirus test"""
    print("="*60)
    print("EXAMPLE 2: Comprehensive Antivirus Test")
    print("="*60)
    
    # Create tester with full configuration
    tester = UltimateAVTester(verbose=True, delay=1.0)
    
    # Enable all test phases
    tester.test_phases = {
        'basic_evasion': True,
        'advanced_evasion': True,
        'modern_evasion': True,
        'behavior_simulation': True,
        'persistence_tests': True,
        'network_tests': True,
        'memory_tests': True
    }
    
    # Run the test
    tester.run_all_tests()

def example_custom_test():
    """Example: Custom test configuration"""
    print("="*60)
    print("EXAMPLE 3: Custom Test Configuration")
    print("="*60)
    
    # Create tester
    tester = UltimateAVTester(verbose=True, delay=2.0)
    
    # Custom configuration - only test specific areas
    tester.test_phases = {
        'basic_evasion': True,
        'advanced_evasion': False,
        'modern_evasion': True,
        'behavior_simulation': True,
        'persistence_tests': False,
        'network_tests': True,
        'memory_tests': False
    }
    
    print("Custom configuration:")
    for phase, enabled in tester.test_phases.items():
        status = "ENABLED" if enabled else "DISABLED"
        print(f"  {phase}: {status}")
    
    # Run the test
    tester.run_all_tests()

def example_individual_modules():
    """Example: Using individual modules"""
    print("="*60)
    print("EXAMPLE 4: Using Individual Modules")
    print("="*60)
    
    from malware_tester import AntivirusEvasion, MaliciousBehaviorSimulator
    from advanced_evasion import AdvancedEvasion
    from modern_evasion import ModernEvasionTechniques
    
    # Create components
    evasion_tester = AntivirusEvasion()
    behavior_simulator = MaliciousBehaviorSimulator(evasion_tester)
    advanced_evasion = AdvancedEvasion(evasion_tester)
    modern_evasion = ModernEvasionTechniques(evasion_tester)
    
    print("Running individual tests...")
    
    # Test specific techniques
    print("\n[TEST] Sandbox Detection...")
    evasion_tester.sandbox_detection()
    
    print("\n[TEST] Keylogger Simulation...")
    behavior_simulator.simulate_keylogger()
    
    print("\n[TEST] Process Hollowing Simulation...")
    advanced_evasion.process_hollowing_simulation()
    
    print("\n[TEST] AI/ML Evasion...")
    modern_evasion.ai_ml_evasion()
    
    # Generate simple report
    total_tests = len(evasion_tester.techniques_tested)
    detected_tests = sum(1 for test in evasion_tester.techniques_tested if test['detected'])
    
    print(f"\nResults: {detected_tests}/{total_tests} techniques detected")
    print(f"Detection rate: {(detected_tests/total_tests)*100:.1f}%")

def interactive_menu():
    """Interactive menu for selecting examples"""
    while True:
        print("\n" + "="*60)
        print("ULTIMATE ANTIVIRUS TESTER - EXAMPLES")
        print("="*60)
        print("Choose an example to run:")
        print()
        print("1. Basic Test (Quick)")
        print("2. Comprehensive Test (Full)")
        print("3. Custom Configuration")
        print("4. Individual Modules")
        print("5. Exit")
        print()
        
        choice = input("Enter your choice (1-5): ").strip()
        
        if choice == '1':
            example_basic_test()
        elif choice == '2':
            example_comprehensive_test()
        elif choice == '3':
            example_custom_test()
        elif choice == '4':
            example_individual_modules()
        elif choice == '5':
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please enter 1-5.")
        
        input("\nPress Enter to continue...")

def main():
    """Main function"""
    print("Ultimate Antivirus Tester - Example Usage")
    print("=" * 50)
    print("⚠️  WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY ⚠️")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # Command line argument provided
        example_type = sys.argv[1].lower()
        
        if example_type == 'basic':
            example_basic_test()
        elif example_type == 'comprehensive':
            example_comprehensive_test()
        elif example_type == 'custom':
            example_custom_test()
        elif example_type == 'individual':
            example_individual_modules()
        else:
            print(f"Unknown example type: {example_type}")
            print("Available types: basic, comprehensive, custom, individual")
    else:
        # Interactive mode
        interactive_menu()

if __name__ == "__main__":
    main()
